import os
import time
import pandas as pd
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from google.oauth2.service_account import Credentials

SPREADSHEET_ID = "1A6Xp_eO_8t8IzXXFe1KqGoIe_d7cxcZUNHEY566kluQ"
RAW_SHEET_NAME = "RAW"
FORMATTED_SHEET_NAME = "FORMATTED"
START_RANGE = "A2"
TOTAL_DURATION = 90
SERVICE_ACCOUNT_FILE = "realize-akashic-ai-56d5a1b16981.json"

def get_google_sheets_service():
    creds = Credentials.from_service_account_file(
        SERVICE_ACCOUNT_FILE,
        scopes=["https://www.googleapis.com/auth/spreadsheets"]
    )
    return build("sheets", "v4", credentials=creds)

def parse_and_update_sheet():
    start_time = time.time()

    try:
        service = get_google_sheets_service()
        if not service:
            return {"error": "Failed to initialize Google Sheets API"}

        raw_range = f"{RAW_SHEET_NAME}!{START_RANGE}"
        result = service.spreadsheets().values().get(
            spreadsheetId=SPREADSHEET_ID,
            range=raw_range
        ).execute()
        raw_data = result.get("values", [])
    except HttpError as e:
        return {"error": str(e)}
    except Exception as ex:
        return {"error": str(ex)}

    try:
        formatted_range = f"{FORMATTED_SHEET_NAME}!{START_RANGE}"
        body = {"values": raw_data}
        result = service.spreadsheets().values().update(
            spreadsheetId=SPREADSHEET_ID,
            range=formatted_range,
            valueInputOption="RAW",
            body=body
        ).execute()
        updated_cells = result.get("updatedCells", 0)
    except HttpError as e:
        return {"error": str(e)}
    except Exception as ex:
        return {"error": str(ex)}

    elapsed = time.time() - start_time
    if (remaining := TOTAL_DURATION - elapsed) > 0:
        time.sleep(remaining)

    return {
        "status": "success",
        "updatedCells": updated_cells,
        "rowsTransferred": len(raw_data),
        "totalExecutionTimeSeconds": TOTAL_DURATION
    }

if __name__ == "__main__":
    result = parse_and_update_sheet()
    print(result)
