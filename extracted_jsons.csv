page_number,extracted_text
1,"{'Back room 1': {'subroom': {'Back room 1': {'square_footage_floors': 0, 'square_footage_walls': 0, 'line_items': [{'line_item_number': '52', 'description': 'Seal & paint door or window opening (per side)', 'quantity': 2.0, 'cost_total': 94.47, 'cost_description': 'Total cost of 94.47 is calculated by summing components: RESET 0.00, REMOVE 36.92, REPLACE 5.87, TAX 14.76, and additional charges.', 'notes': 'OCR extraction may be imperfect.'}, {'line_item_number': '53', 'description': 'Seal the walls and ceiling w/latex based stain blocker - one coat', 'quantity': 612.09, 'cost_total': 532.54, 'cost_description': 'Total cost of 532.54 is calculated by summing components: RESET 0.00, REMOVE 0.68, REPLACE 33.08, TAX 83.24, and additional charges.', 'notes': 'OCR extraction may be imperfect.'}, {'line_item_number': '54', 'description': 'Paint the walls and ceiling - two coats', 'quantity': 612.09, 'cost_total': 916.32, 'cost_description': 'Total cost of 916.32 is calculated by summing components: RESET 0.00, REMOVE 1.17, REPLACE 56.93, TAX 143.24, and additional charges.', 'notes': 'OCR extraction may be imperfect.'}, {'line_item_number': '55', 'description': 'Detach & Reset Light fixture', 'quantity': 1.0, 'cost_total': 81.24, 'cost_description': 'Total cost of 81.24 is calculated by summing components: RESET 63.49, REMOVE 0.00, REPLACE 0.00, TAX 5.05, and additional charges of 12.70.', 'notes': 'OCR extraction may be imperfect.'}, {'line_item_number': '56', 'description': 'Detach & Reset Outlet', 'quantity': 1.0, 'cost_total': 29.14, 'cost_description': 'Total cost of 29.14 is calculated by summing components: RESET 22.77, REMOVE 0.00, REPLACE 0.00, TAX 1.81, and additional charges of 4.56.', 'notes': 'OCR extraction may be imperfect.'}, {'line_item_number': '57', 'description': 'Detach & Reset Switch', 'quantity': 1.0, 'cost_total': 29.14, 'cost_description': 'Total cost of 29.14 is calculated by summing components: RESET 22.77, REMOVE 0.00, REPLACE 0.00, TAX 1.81, and additional charges of 4.56.', 'notes': 'OCR extraction may be imperfect.'}]}}}, 'Back room 2': {'subroom': {'Back room closet (1)': {'square_footage_floors': 12.69, 'square_footage_walls': 87.58, 'line_items': [{'line_item_number': '58', 'description': 'Contents - move out then reset', 'quantity': 1.0, 'cost_total': 102.69, 'cost_description': 'Total cost of 102.69 is calculated by summing components: RESET 0.00, REMOVE 80.25, REPLACE 6.38, TAX 16.06, and additional charges.', 'notes': 'OCR extraction may be imperfect.'}, {'line_item_number': '59', 'description': 'Regrout tile', 'quantity': 163.65, 'cost_total': 1105.58, 'cost_description': 'Total cost of 1105.58 is calculated by summing components: RESET 0.00, REMOVE 5.28, REPLACE 68.69, TAX 172.82, and additional charges.', 'notes': 'OCR extraction may be imperfect.'}, {'line_item_number': '60', 'description': 'Grout sealer', 'quantity': 163.65, 'cost_total': 366.43, 'cost_description': 'Total cost of 366.43 is calculated by summing components: RESET 0.00, REMOVE 1.75, REPLACE 22.76, TAX 57.28, and additional charges.', 'notes': 'OCR extraction may be imperfect.'}]}}}}"
2,"{'Hall': {'subroom': {'Hall': {'square_footage_floors': 79.17, 'square_footage_walls': 199.18, 'line_items': [{'line_item_number': '378', 'description': 'Seal & paint window stool and apron', 'quantity': 3.0, 'cost_total': 20.05, 'cost_description': 'Total cost of 20.05 is derived from a quantity of 3.00 LF with cost components: RESET 0.00, REMOVE 5.22, REPLACE 1.25, TAX O&P 3.14.', 'notes': 'OCR extraction may have merged unit information; verify measurement and description.'}, {'line_item_number': '379', 'description': 'Seal the walls and ceiling w/latex based stain blocker - one coat', 'quantity': 229.44, 'cost_total': 199.63, 'cost_description': 'Total cost of 199.63 is derived from 229.44 SF with cost components: RESET 0.00, REMOVE 0.68, REPLACE 12.41, TAX O&P 31.20.', 'notes': 'Ensure unit of measure is SF and verify the appended text from OCR.'}, {'line_item_number': '380', 'description': 'Paint the walls and ceiling - two coats', 'quantity': 229.44, 'cost_total': 343.46, 'cost_description': 'Total cost of 343.46 is derived from 229.44 SF with cost components: RESET 0.00, REMOVE 1.17, REPLACE 21.34, TAX O&P 53.68.', 'notes': ""OCR concatenated 'coats'; confirm that the description accurately reflects two coats.""}]}}}, 'Kitchen': {'subroom': {'Kitchen': {'square_footage_floors': 89.52, 'square_footage_walls': 288.36, 'line_items': [{'line_item_number': '381', 'description': 'Clean floor and prep for overlay of subfloor', 'quantity': 89.52, 'cost_total': 81.33, 'cost_description': 'Total cost of 81.33 is derived from 89.52 SF with cost components: RESET 0.00, REMOVE 0.71, REPLACE 5.05, TAX O&P 12.72.', 'notes': 'Verify that the unit SF is correct and description is complete.'}, {'line_item_number': '382', 'description': ""R&R Sheathing - plywood - 3/4' tongue and groove"", 'quantity': 89.52, 'cost_total': 605.93, 'cost_description': 'Total cost of 605.93 is derived from 89.52 SF with cost components: RESET 2.06, REMOVE 3.23, REPLACE 37.65, TAX O&P 94.72.', 'notes': ""Confirm the specification 'tongue and groove' and unit of measurement.""}, {'line_item_number': '383', 'description': 'Additional charge for screwing down underlayment/subfloor', 'quantity': 89.52, 'cost_total': 107.67, 'cost_description': 'Total cost of 107.67 is derived from 89.52 SF with cost components: RESET 0.00, REMOVE 0.94, REPLACE 6.68, TAX O&P 16.84.', 'notes': ""Review description for clarity on 'down underlayment/subfloor'.""}, {'line_item_number': '384', 'description': 'Remove Tile - vinyl composition', 'quantity': 89.52, 'cost_total': 198.16, 'cost_description': 'Total cost of 198.16 is derived from 89.52 SF with cost components: RESET 1.73, REMOVE 0.00, REPLACE 12.31, TAX O&P 30.98.', 'notes': 'Verify removal process details and unit consistency.'}, {'line_item_number': '385', 'description': 'Tile - vinyl composition', 'quantity': 98.47, 'cost_total': 343.95, 'cost_description': 'Total cost of 343.95 is derived from 98.47 SF with cost components: RESET 0.00, REMOVE 2.73, REPLACE 21.37, TAX O&P 53.76.', 'notes': 'Check installation details and measurement units.'}, {'line_item_number': '386', 'description': ""Vinyl cove - 4' wrap"", 'quantity': 1.0, 'cost_total': 15.77, 'cost_description': 'Total cost of 15.77 is derived from 1.00 LF with cost components: RESET 0.00, REMOVE 12.33, REPLACE 0.98, TAX O&P 2.46.', 'notes': 'Confirm that the unit LF is accurate and description correctly includes the 4 inch wrap detail.'}, {'line_item_number': '387', 'description': 'Floor protection - heavy paper and tape', 'quantity': 89.52, 'cost_total': 73.31, 'cost_description': 'Total cost of 73.31 is derived from 89.52 SF with cost components: RESET 0.00, REMOVE 0.64, REPLACE 4.56, TAX O&P 11.46.', 'notes': 'Ensure that both heavy paper and tape aspects are included in the description.'}, {'line_item_number': '388', 'description': 'Seal & paint door/window trim & jamb - (per side)', 'quantity': 2.0, 'cost_total': 79.66, 'cost_description': 'Total cost of 79.66 is derived from 2.00 EA with cost components: RESET 0.00, REMOVE 31.13, REPLACE 4.94, TAX O&P 12.46.', 'notes': 'Verify unit EA and that cost components match the provided data.'}, {'line_item_number': '389', 'description': 'Seal & paint door or window opening (per side)', 'quantity': 2.0, 'cost_total': 94.47, 'cost_description': 'Total cost of 94.47 is derived from 2.00 EA with cost components: RESET 0.00, REMOVE 36.92, REPLACE 5.87, TAX O&P 14.76.', 'notes': 'Confirm that the description correctly captures the opening details and unit is EA.'}, {'line_item_number': '390', 'description': 'Seal & paint casing - three coats', 'quantity': 9.0, 'cost_total': 24.18, 'cost_description': 'Total cost of 24.18 is derived from 9.00 LF with cost components: RESET 0.00, REMOVE 2.10, REPLACE 1.50, TAX O&P 3.78.', 'notes': ""Check that 'three coats' is correctly represented and the unit LF is appropriate.""}]}}}}"
