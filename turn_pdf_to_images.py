from pdf2image import convert_from_path
import os

def pdf_to_images(pdf_path, output_folder):
    """
    Converts a PDF into an array of images, saving each page as an image file.
    :param pdf_path: Path to the PDF file.
    :param output_folder: Folder where images will be saved.
    """
    if not os.path.exists(output_folder):
        os.makedirs(output_folder)
    
    images = convert_from_path(pdf_path)
    image_paths = []
    
    for i, image in enumerate(images, start=1):
        image_path = os.path.join(output_folder, f"{i}.jpg")
        image.save(image_path, "JPEG")
        image_paths.append(image_path)
    
    return image_paths

if __name__ == "__main__":
    pdf_file = "Sanchez - Atlas - Repair Estimate.pdf"  # Replace with the actual PDF path
    output_dir = "output_images"  # Replace with your desired output directory
    result = pdf_to_images(pdf_file, output_dir)
    print("Images saved:", result)