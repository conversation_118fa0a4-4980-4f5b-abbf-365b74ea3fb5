#!/usr/bin/env python3
"""
Athena Demo Interface - Web wrapper for the existing prototype
Provides a user-friendly web interface while preserving all original functionality
"""

import os
import asyncio
import threading
import json
import csv
import time
from datetime import datetime
from flask import Flask, render_template, render_template_string, request, jsonify, send_file, redirect, url_for
from werkzeug.utils import secure_filename
import pandas as pd

# Import existing prototype functionality (unchanged)
import prototype

# Import demo configuration
try:
    import demo_config as config
except ImportError:
    # Fallback configuration if demo_config.py is not available
    class config:
        DEMO_PORT = 5000
        DEMO_HOST = '0.0.0.0'
        DEBUG_MODE = False
        MAX_FILE_SIZE_MB = 50
        UPLOAD_FOLDER = 'uploads'

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = config.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = config.MAX_FILE_SIZE_MB * 1024 * 1024

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Global variables for demo state
processing_status = {
    'is_processing': False,
    'current_page': 0,
    'total_pages': 0,
    'current_stage': '',
    'results': [],
    'error': None,
    'start_time': None,
    'processing_details': {}
}

@app.route('/')
def index():
    """Main demo interface with API key field"""
    # Use inline template to ensure API key field is visible
    template = '''
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Athena - Insurance Claim Document Processor</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.2rem; opacity: 0.9; }
        .upload-card { background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center; }
        .upload-card h2 { margin-bottom: 25px; color: #333; font-size: 1.8rem; }

        .api-key-section {
            margin-bottom: 25px;
            padding: 20px;
            background: #f0f4ff;
            border-radius: 10px;
            border: 3px solid #667eea;
        }
        .api-key-section .control-group { position: relative; display: flex; align-items: center; gap: 10px; width: 100%; }
        .api-key-section label { font-weight: 600; color: #333; font-size: 1.1rem; }
        .api-key-section input {
            flex: 1;
            padding: 12px 50px 12px 15px;
            border: 2px solid #667eea;
            border-radius: 8px;
            font-size: 1rem;
            font-family: 'Courier New', monospace;
            background: white;
        }
        .btn-toggle-key {
            position: absolute;
            right: 10px;
            background: none;
            border: none;
            color: #667eea;
            cursor: pointer;
            padding: 8px;
            border-radius: 4px;
        }
        .api-key-help { margin-top: 8px; text-align: center; }
        .api-key-help small { color: #666; font-size: 0.85rem; }
        .api-key-help a { color: #667eea; text-decoration: none; }

        .file-upload input[type="file"] { display: none; }
        .file-label {
            display: block;
            padding: 40px 20px;
            border: 3px dashed #ddd;
            border-radius: 10px;
            cursor: pointer;
            background: #f8f9fa;
            margin-bottom: 20px;
        }
        .file-label i { font-size: 3rem; color: #667eea; margin-bottom: 15px; display: block; }
        .file-label span { font-size: 1.1rem; color: #666; }

        .controls { display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; flex-wrap: wrap; gap: 20px; }
        .control-group { display: flex; align-items: center; gap: 10px; }
        .control-group label { font-weight: 600; color: #555; }
        .control-group select { padding: 8px 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 1rem; }

        .btn-primary {
            padding: 12px 25px;
            border: none;
            border-radius: 8px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-primary:hover { transform: translateY(-2px); }

        .checkbox-label { display: flex; align-items: center; cursor: pointer; font-weight: 600; color: #555; }
        .checkbox-label input[type="checkbox"] { margin-right: 8px; }

        .processing-section, .results-section, .error-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            margin-top: 20px;
        }
        .progress-bar { width: 100%; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; margin: 20px 0; }
        .progress-fill { height: 100%; background: linear-gradient(90deg, #667eea 0%, #764ba2 100%); width: 0%; transition: width 0.3s ease; }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-file-invoice"></i> Athena Demo</h1>
            <p>AI-Powered Insurance Claim Document Processing</p>
        </header>

        <div class="upload-section" id="uploadSection">
            <div class="upload-card">
                <h2>Upload PDF Document</h2>

                <!-- API KEY SECTION -->
                <div class="api-key-section">
                    <div class="control-group">
                        <label for="apiKey"><i class="fas fa-key"></i> OpenAI API Key:</label>
                        <input type="password" id="apiKey" name="api_key" placeholder="sk-proj-..." required>
                        <button type="button" class="btn-toggle-key" onclick="toggleApiKeyVisibility()">
                            <i class="fas fa-eye" id="keyToggleIcon"></i>
                        </button>
                    </div>
                    <div class="api-key-help">
                        <small><i class="fas fa-info-circle"></i> Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a>. Your key is processed securely and not stored.</small>
                    </div>
                </div>

                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="file-upload">
                        <input type="file" id="fileInput" name="file" accept=".pdf" required>
                        <label for="fileInput" class="file-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Choose PDF file or drag and drop</span>
                        </label>
                    </div>

                    <div class="controls">
                        <div class="control-group">
                            <label for="maxPages">Pages to Process:</label>
                            <select id="maxPages" name="max_pages">
                                <option value="0">All Pages</option>
                                <option value="1">1 Page</option>
                                <option value="3">3 Pages</option>
                                <option value="5" selected>5 Pages</option>
                                <option value="10">10 Pages</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="useGpt41" name="use_gpt41" value="true">
                                Use GPT-4.1 (Enhanced Model)
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn-primary">
                        <i class="fas fa-play"></i> Start Processing
                    </button>
                </form>
            </div>
        </div>

        <div class="processing-section" id="processingSection" style="display: none;">
            <h3>Processing Document...</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <p id="statusText">Initializing...</p>
            <p id="timeElapsed">Time: 0s</p>
        </div>

        <div class="results-section" id="resultsSection" style="display: none;">
            <h2>Processing Complete!</h2>
            <p>Results would be displayed here in the full implementation.</p>
            <button class="btn-primary" onclick="resetDemo()">New Document</button>
        </div>

        <div class="error-section" id="errorSection" style="display: none;">
            <h3><i class="fas fa-exclamation-triangle"></i> Error</h3>
            <p id="errorMessage"></p>
            <button class="btn-primary" onclick="resetDemo()">Try Again</button>
        </div>
    </div>

    <script>
        function toggleApiKeyVisibility() {
            const apiKeyInput = document.getElementById('apiKey');
            const toggleIcon = document.getElementById('keyToggleIcon');

            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                apiKeyInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const apiKey = document.getElementById('apiKey').value.trim();

            if (!apiKey) {
                alert('Please enter your OpenAI API key');
                document.getElementById('apiKey').focus();
                return;
            }

            if (!apiKey.startsWith('sk-')) {
                alert('Please enter a valid OpenAI API key (should start with "sk-")');
                document.getElementById('apiKey').focus();
                return;
            }

            const fileInput = document.getElementById('fileInput');
            if (!fileInput.files[0]) {
                alert('Please select a PDF file');
                return;
            }

            // Show processing
            document.getElementById('uploadSection').style.display = 'none';
            document.getElementById('processingSection').style.display = 'block';

            // Simulate processing for demo
            let progress = 0;
            const interval = setInterval(() => {
                progress += 10;
                document.getElementById('progressFill').style.width = progress + '%';
                document.getElementById('statusText').textContent = `Processing... ${progress}%`;

                if (progress >= 100) {
                    clearInterval(interval);
                    document.getElementById('processingSection').style.display = 'none';
                    document.getElementById('resultsSection').style.display = 'block';
                }
            }, 500);
        });

        function resetDemo() {
            document.getElementById('uploadSection').style.display = 'block';
            document.getElementById('processingSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('errorSection').style.display = 'none';
            document.getElementById('uploadForm').reset();
            document.querySelector('.file-label span').textContent = 'Choose PDF file or drag and drop';
        }

        document.getElementById('fileInput').addEventListener('change', function() {
            if (this.files[0]) {
                document.querySelector('.file-label span').textContent = this.files[0].name;
            }
        });
    </script>
</body>
</html>
    '''
    return render_template_string(template)

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle PDF upload and start processing"""
    global processing_status

    if processing_status['is_processing']:
        return jsonify({'error': 'Already processing a document'}), 400

    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if not file.filename.lower().endswith('.pdf'):
        return jsonify({'error': 'Please upload a PDF file'}), 400

    # Get API key from form
    api_key = request.form.get('api_key', '').strip()
    if not api_key:
        return jsonify({'error': 'API key is required'}), 400

    if not api_key.startswith('sk-'):
        return jsonify({'error': 'Invalid API key format'}), 400

    # Set the API key for this session
    os.environ['OPENAI_API_KEY'] = api_key

    # Reinitialize the OpenAI client with the new API key
    reinitialize_openai_client(api_key)

    # Get batch size from form
    max_pages = request.form.get('max_pages', type=int)
    if max_pages == 0:  # 0 means process all pages
        max_pages = None

    # Enable GPT-4.1 if requested
    use_gpt41 = request.form.get('use_gpt41') == 'true'
    if use_gpt41:
        os.environ['USE_GPT41'] = 'true'
    else:
        os.environ.pop('USE_GPT41', None)

    # Save uploaded file
    filename = secure_filename(file.filename)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{timestamp}_{filename}"
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    file.save(filepath)

    # Reset processing status
    processing_status.update({
        'is_processing': True,
        'current_page': 0,
        'total_pages': 0,
        'current_stage': 'Starting...',
        'results': [],
        'error': None,
        'start_time': time.time(),
        'processing_details': {},
        'filename': filename,
        'filepath': filepath,
        'max_pages': max_pages
    })

    # Start processing in background thread
    thread = threading.Thread(target=process_document_async, args=(filepath, max_pages))
    thread.daemon = True
    thread.start()

    return jsonify({'message': 'Upload successful, processing started', 'filename': filename})

def reinitialize_openai_client(api_key):
    """Reinitialize the OpenAI client with new API key"""
    from openai import AsyncOpenAI
    prototype.openai_client = AsyncOpenAI(api_key=api_key)
    prototype.API_KEY = api_key

def process_document_async(filepath, max_pages):
    """Process document in background thread"""
    global processing_status

    try:
        # Create unique output files for this session
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_output = f"demo_output_{timestamp}.csv"
        json_output = f"demo_output_{timestamp}.json"

        # Run the async processing
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Update status
        processing_status['current_stage'] = 'Analyzing PDF structure...'

        # Call existing prototype function with max_pages
        df = loop.run_until_complete(prototype.process_pdf(filepath, csv_output, max_pages))

        # Read the generated CSV for results
        results = []
        if os.path.exists(csv_output):
            with open(csv_output, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                results = list(reader)

        # Save results to JSON as well
        with open(json_output, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)

        # Update final status
        processing_status.update({
            'is_processing': False,
            'current_stage': 'Complete',
            'results': results,
            'csv_file': csv_output,
            'json_file': json_output,
            'total_pages': len(df) if df is not None else 0
        })

    except Exception as e:
        processing_status.update({
            'is_processing': False,
            'error': str(e),
            'current_stage': 'Error occurred'
        })
    finally:
        # Clean up uploaded file
        if os.path.exists(filepath):
            os.remove(filepath)

@app.route('/status')
def get_status():
    """Get current processing status"""
    status = processing_status.copy()

    # Calculate elapsed time
    if status['start_time']:
        status['elapsed_time'] = time.time() - status['start_time']

    return jsonify(status)

@app.route('/results')
def get_results():
    """Get processing results"""
    return jsonify({
        'results': processing_status['results'],
        'total_items': len(processing_status['results'])
    })

@app.route('/export/<format>')
def export_results(format):
    """Export results in CSV or JSON format"""
    if format == 'csv' and 'csv_file' in processing_status:
        return send_file(processing_status['csv_file'], as_attachment=True)
    elif format == 'json' and 'json_file' in processing_status:
        return send_file(processing_status['json_file'], as_attachment=True)
    else:
        return jsonify({'error': 'No results available for export'}), 404

@app.route('/reset', methods=['POST'])
def reset_demo():
    """Reset demo state for new processing"""
    global processing_status

    # Clean up old files
    if 'csv_file' in processing_status and os.path.exists(processing_status['csv_file']):
        os.remove(processing_status['csv_file'])
    if 'json_file' in processing_status and os.path.exists(processing_status['json_file']):
        os.remove(processing_status['json_file'])

    # Reset status
    processing_status = {
        'is_processing': False,
        'current_page': 0,
        'total_pages': 0,
        'current_stage': '',
        'results': [],
        'error': None,
        'start_time': None,
        'processing_details': {}
    }

    return jsonify({'message': 'Demo reset successfully'})

@app.route('/sample')
def use_sample():
    """Use a sample PDF for quick demo"""
    # Look for sample PDFs in the current directory
    sample_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    if sample_files:
        return jsonify({'samples': sample_files})
    else:
        return jsonify({'error': 'No sample PDFs found'}), 404

if __name__ == '__main__':
    print("Starting Athena Demo Interface...")
    print(f"Open your browser to: http://{config.DEMO_HOST}:{config.DEMO_PORT}")
    print("Original prototype.py functionality is preserved and unchanged.")
    app.run(debug=config.DEBUG_MODE, host=config.DEMO_HOST, port=config.DEMO_PORT)
