#!/usr/bin/env python3
"""
Athena Demo Interface - Web wrapper for the existing prototype
Provides a user-friendly web interface while preserving all original functionality
"""

import os
import asyncio
import threading
import json
import csv
import time
from datetime import datetime
from flask import Flask, render_template, request, jsonify, send_file, redirect, url_for
from werkzeug.utils import secure_filename
import pandas as pd

# Import existing prototype functionality (unchanged)
import prototype

# Import demo configuration
try:
    import demo_config as config
except ImportError:
    # Fallback configuration if demo_config.py is not available
    class config:
        DEMO_PORT = 5000
        DEMO_HOST = '0.0.0.0'
        DEBUG_MODE = False
        MAX_FILE_SIZE_MB = 50
        UPLOAD_FOLDER = 'uploads'

app = Flask(__name__)
app.config['UPLOAD_FOLDER'] = config.UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = config.MAX_FILE_SIZE_MB * 1024 * 1024

# Ensure upload directory exists
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Global variables for demo state
processing_status = {
    'is_processing': False,
    'current_page': 0,
    'total_pages': 0,
    'current_stage': '',
    'results': [],
    'error': None,
    'start_time': None,
    'processing_details': {}
}

@app.route('/')
def index():
    """Main demo interface"""
    return render_template('demo.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    """Handle PDF upload and start processing"""
    global processing_status

    if processing_status['is_processing']:
        return jsonify({'error': 'Already processing a document'}), 400

    if 'file' not in request.files:
        return jsonify({'error': 'No file uploaded'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'No file selected'}), 400

    if not file.filename.lower().endswith('.pdf'):
        return jsonify({'error': 'Please upload a PDF file'}), 400

    # Get batch size from form
    max_pages = request.form.get('max_pages', type=int)
    if max_pages == 0:  # 0 means process all pages
        max_pages = None

    # Enable GPT-4.1 if requested
    use_gpt41 = request.form.get('use_gpt41') == 'true'
    if use_gpt41:
        os.environ['USE_GPT41'] = 'true'
    else:
        os.environ.pop('USE_GPT41', None)

    # Save uploaded file
    filename = secure_filename(file.filename)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{timestamp}_{filename}"
    filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
    file.save(filepath)

    # Reset processing status
    processing_status.update({
        'is_processing': True,
        'current_page': 0,
        'total_pages': 0,
        'current_stage': 'Starting...',
        'results': [],
        'error': None,
        'start_time': time.time(),
        'processing_details': {},
        'filename': filename,
        'filepath': filepath,
        'max_pages': max_pages
    })

    # Start processing in background thread
    thread = threading.Thread(target=process_document_async, args=(filepath, max_pages))
    thread.daemon = True
    thread.start()

    return jsonify({'message': 'Upload successful, processing started', 'filename': filename})

def process_document_async(filepath, max_pages):
    """Process document in background thread"""
    global processing_status

    try:
        # Create unique output files for this session
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        csv_output = f"demo_output_{timestamp}.csv"
        json_output = f"demo_output_{timestamp}.json"

        # Run the async processing
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        # Update status
        processing_status['current_stage'] = 'Analyzing PDF structure...'

        # Call existing prototype function with max_pages
        df = loop.run_until_complete(prototype.process_pdf(filepath, csv_output, max_pages))

        # Read the generated CSV for results
        results = []
        if os.path.exists(csv_output):
            with open(csv_output, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                results = list(reader)

        # Save results to JSON as well
        with open(json_output, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2)

        # Update final status
        processing_status.update({
            'is_processing': False,
            'current_stage': 'Complete',
            'results': results,
            'csv_file': csv_output,
            'json_file': json_output,
            'total_pages': len(df) if df is not None else 0
        })

    except Exception as e:
        processing_status.update({
            'is_processing': False,
            'error': str(e),
            'current_stage': 'Error occurred'
        })
    finally:
        # Clean up uploaded file
        if os.path.exists(filepath):
            os.remove(filepath)

@app.route('/status')
def get_status():
    """Get current processing status"""
    status = processing_status.copy()

    # Calculate elapsed time
    if status['start_time']:
        status['elapsed_time'] = time.time() - status['start_time']

    return jsonify(status)

@app.route('/results')
def get_results():
    """Get processing results"""
    return jsonify({
        'results': processing_status['results'],
        'total_items': len(processing_status['results'])
    })

@app.route('/export/<format>')
def export_results(format):
    """Export results in CSV or JSON format"""
    if format == 'csv' and 'csv_file' in processing_status:
        return send_file(processing_status['csv_file'], as_attachment=True)
    elif format == 'json' and 'json_file' in processing_status:
        return send_file(processing_status['json_file'], as_attachment=True)
    else:
        return jsonify({'error': 'No results available for export'}), 404

@app.route('/reset', methods=['POST'])
def reset_demo():
    """Reset demo state for new processing"""
    global processing_status

    # Clean up old files
    if 'csv_file' in processing_status and os.path.exists(processing_status['csv_file']):
        os.remove(processing_status['csv_file'])
    if 'json_file' in processing_status and os.path.exists(processing_status['json_file']):
        os.remove(processing_status['json_file'])

    # Reset status
    processing_status = {
        'is_processing': False,
        'current_page': 0,
        'total_pages': 0,
        'current_stage': '',
        'results': [],
        'error': None,
        'start_time': None,
        'processing_details': {}
    }

    return jsonify({'message': 'Demo reset successfully'})

@app.route('/sample')
def use_sample():
    """Use a sample PDF for quick demo"""
    # Look for sample PDFs in the current directory
    sample_files = [f for f in os.listdir('.') if f.lower().endswith('.pdf')]
    if sample_files:
        return jsonify({'samples': sample_files})
    else:
        return jsonify({'error': 'No sample PDFs found'}), 404

if __name__ == '__main__':
    print("Starting Athena Demo Interface...")
    print(f"Open your browser to: http://{config.DEMO_HOST}:{config.DEMO_PORT}")
    print("Original prototype.py functionality is preserved and unchanged.")
    app.run(debug=config.DEBUG_MODE, host=config.DEMO_HOST, port=config.DEMO_PORT)
