"""
Athena Demo Configuration
Customize demo behavior without modifying core files
"""

# Demo Interface Settings
DEMO_PORT = 5000
DEMO_HOST = '0.0.0.0'
DEBUG_MODE = False

# File Upload Settings
MAX_FILE_SIZE_MB = 50
ALLOWED_EXTENSIONS = ['.pdf']
UPLOAD_FOLDER = 'uploads'

# Processing Settings
DEFAULT_BATCH_SIZE = 5
MAX_BATCH_SIZE = 50
BATCH_SIZE_OPTIONS = [1, 3, 5, 10, 20, 0]  # 0 = All pages

# Model Settings
DEFAULT_MODEL = 'gpt-4o-mini'
ENHANCED_MODEL = 'gpt-4.1-2025-04-14'
ENABLE_MODEL_SELECTION = True

# UI Customization
DEMO_TITLE = "Athena - Insurance Claim Document Processor"
COMPANY_NAME = "Realize Analytics"
THEME_COLOR = "#667eea"

# Processing Timeouts (seconds)
PROCESSING_TIMEOUT = 300
STATUS_UPDATE_INTERVAL = 1

# Export Settings
EXPORT_FORMATS = ['csv', 'json']
INCLUDE_TIMESTAMP_IN_EXPORTS = True

# Demo Features
ENABLE_SAMPLE_DOCUMENTS = True
ENABLE_PROCESSING_DETAILS = True
ENABLE_PROGRESS_TRACKING = True
ENABLE_REAL_TIME_UPDATES = True

# Advanced Settings
AUTO_CLEANUP_UPLOADS = True
CLEANUP_DELAY_MINUTES = 60
MAX_CONCURRENT_PROCESSING = 1

# Error Handling
SHOW_DETAILED_ERRORS = False
ENABLE_ERROR_LOGGING = True
AUTO_RETRY_ON_FAILURE = True
MAX_RETRY_ATTEMPTS = 3
