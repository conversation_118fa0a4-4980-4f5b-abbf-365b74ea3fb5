<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Athena - Insurance Claim Document Processor</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='demo.css') }}">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-file-invoice"></i> Athena Demo</h1>
            <p>AI-Powered Insurance Claim Document Processing</p>
        </header>

        <div class="upload-section" id="uploadSection">
            <div class="upload-card">
                <h2>Upload PDF Document</h2>
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="file-upload">
                        <input type="file" id="fileInput" name="file" accept=".pdf" required>
                        <label for="fileInput" class="file-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Choose PDF file or drag and drop</span>
                        </label>
                    </div>

                    <div class="api-key-section">
                        <div class="control-group full-width">
                            <label for="apiKey">OpenAI API Key:</label>
                            <input type="password" id="apiKey" name="api_key" placeholder="sk-proj-..." required>
                            <button type="button" class="btn-toggle-key" onclick="toggleApiKeyVisibility()">
                                <i class="fas fa-eye" id="keyToggleIcon"></i>
                            </button>
                        </div>
                        <div class="api-key-help">
                            <small><i class="fas fa-info-circle"></i> Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a>. Your key is processed securely and not stored.</small>
                        </div>
                    </div>

                    <div class="controls">
                        <div class="control-group">
                            <label for="maxPages">Pages to Process:</label>
                            <select id="maxPages" name="max_pages">
                                <option value="0">All Pages</option>
                                <option value="1">1 Page</option>
                                <option value="3">3 Pages</option>
                                <option value="5" selected>5 Pages</option>
                                <option value="10">10 Pages</option>
                            </select>
                        </div>

                        <div class="control-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="useGpt41" name="use_gpt41" value="true">
                                <span class="checkmark"></span>
                                Use GPT-4.1 (Enhanced Model)
                            </label>
                        </div>
                    </div>

                    <button type="submit" class="btn-primary" id="processBtn">
                        <i class="fas fa-play"></i> Start Processing
                    </button>
                </form>
            </div>
        </div>

        <div class="processing-section" id="processingSection" style="display: none;">
            <div class="progress-card">
                <h3>Processing Document...</h3>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill"></div>
                </div>
                <div class="status-info">
                    <p id="statusText">Initializing...</p>
                    <p id="timeElapsed">Time: 0s</p>
                </div>
            </div>
        </div>

        <div class="results-section" id="resultsSection" style="display: none;">
            <div class="results-header">
                <h2>Extraction Results</h2>
                <div class="results-controls">
                    <button class="btn-secondary" onclick="exportResults('csv')">
                        <i class="fas fa-download"></i> Export CSV
                    </button>
                    <button class="btn-secondary" onclick="exportResults('json')">
                        <i class="fas fa-download"></i> Export JSON
                    </button>
                    <button class="btn-secondary" onclick="resetDemo()">
                        <i class="fas fa-refresh"></i> New Document
                    </button>
                </div>
            </div>

            <div class="summary-stats" id="summaryStats">
                <!-- Summary statistics will be populated here -->
            </div>

            <div class="results-table-container">
                <table class="results-table" id="resultsTable">
                    <thead>
                        <tr>
                            <th>Page</th>
                            <th>Room</th>
                            <th>Subroom</th>
                            <th>Description</th>
                            <th>Quantity</th>
                            <th>Cost Total</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody id="resultsBody">
                        <!-- Results will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>

        <div class="error-section" id="errorSection" style="display: none;">
            <div class="error-card">
                <h3><i class="fas fa-exclamation-triangle"></i> Processing Error</h3>
                <p id="errorMessage"></p>
                <button class="btn-primary" onclick="resetDemo()">Try Again</button>
            </div>
        </div>
    </div>

    <script>
        let statusInterval;

        // File upload handling with API key validation
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();

            // Validate API key first
            const apiKey = document.getElementById('apiKey').value.trim();

            if (!apiKey) {
                alert('Please enter your OpenAI API key');
                document.getElementById('apiKey').focus();
                return false;
            }

            if (!apiKey.startsWith('sk-')) {
                alert('Please enter a valid OpenAI API key (should start with "sk-")');
                document.getElementById('apiKey').focus();
                return false;
            }

            const formData = new FormData(this);
            const fileInput = document.getElementById('fileInput');

            if (!fileInput.files[0]) {
                alert('Please select a PDF file');
                return;
            }

            // Show processing section
            document.getElementById('uploadSection').style.display = 'none';
            document.getElementById('processingSection').style.display = 'block';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('errorSection').style.display = 'none';

            // Start upload
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    showError(data.error);
                } else {
                    startStatusPolling();
                }
            })
            .catch(error => {
                showError('Upload failed: ' + error.message);
            });
        });

        function startStatusPolling() {
            statusInterval = setInterval(checkStatus, 1000);
        }

        function checkStatus() {
            fetch('/status')
            .then(response => response.json())
            .then(status => {
                updateProgress(status);

                if (!status.is_processing) {
                    clearInterval(statusInterval);

                    if (status.error) {
                        showError(status.error);
                    } else {
                        showResults();
                    }
                }
            })
            .catch(error => {
                clearInterval(statusInterval);
                showError('Status check failed: ' + error.message);
            });
        }

        function updateProgress(status) {
            const progressFill = document.getElementById('progressFill');
            const statusText = document.getElementById('statusText');
            const timeElapsed = document.getElementById('timeElapsed');

            // Update progress bar
            if (status.total_pages > 0) {
                const progress = (status.current_page / status.total_pages) * 100;
                progressFill.style.width = progress + '%';
            }

            // Update status text
            statusText.textContent = status.current_stage || 'Processing...';

            // Update elapsed time
            if (status.elapsed_time) {
                timeElapsed.textContent = `Time: ${Math.round(status.elapsed_time)}s`;
            }
        }

        function showResults() {
            fetch('/results')
            .then(response => response.json())
            .then(data => {
                document.getElementById('processingSection').style.display = 'none';
                document.getElementById('resultsSection').style.display = 'block';

                populateResults(data.results);
                updateSummaryStats(data.results);
            })
            .catch(error => {
                showError('Failed to load results: ' + error.message);
            });
        }

        function populateResults(results) {
            const tbody = document.getElementById('resultsBody');
            tbody.innerHTML = '';

            results.forEach(row => {
                const tr = document.createElement('tr');
                tr.innerHTML = `
                    <td>${row.page_number || ''}</td>
                    <td>${row.room_name || ''}</td>
                    <td>${row.subroom_name || ''}</td>
                    <td>${row.description || ''}</td>
                    <td>${row.quantity || ''}</td>
                    <td>$${parseFloat(row.cost_total || 0).toFixed(2)}</td>
                    <td>${row.notes || ''}</td>
                `;
                tbody.appendChild(tr);
            });
        }

        function updateSummaryStats(results) {
            const totalItems = results.length;
            const totalCost = results.reduce((sum, row) => sum + parseFloat(row.cost_total || 0), 0);
            const uniqueRooms = new Set(results.map(row => row.room_name)).size;

            document.getElementById('summaryStats').innerHTML = `
                <div class="stat-card">
                    <div class="stat-number">${totalItems}</div>
                    <div class="stat-label">Line Items</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">${uniqueRooms}</div>
                    <div class="stat-label">Rooms</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number">$${totalCost.toFixed(2)}</div>
                    <div class="stat-label">Total Cost</div>
                </div>
            `;
        }

        function exportResults(format) {
            window.open(`/export/${format}`, '_blank');
        }

        function resetDemo() {
            fetch('/reset', { method: 'POST' })
            .then(() => {
                document.getElementById('uploadSection').style.display = 'block';
                document.getElementById('processingSection').style.display = 'none';
                document.getElementById('resultsSection').style.display = 'none';
                document.getElementById('errorSection').style.display = 'none';

                // Reset form
                document.getElementById('uploadForm').reset();

                // Reset file label text
                document.querySelector('.file-label span').textContent = 'Choose PDF file or drag and drop';
            });
        }

        function showError(message) {
            document.getElementById('uploadSection').style.display = 'none';
            document.getElementById('processingSection').style.display = 'none';
            document.getElementById('resultsSection').style.display = 'none';
            document.getElementById('errorSection').style.display = 'block';

            document.getElementById('errorMessage').textContent = message;
        }

        // File drag and drop
        const fileLabel = document.querySelector('.file-label');
        const fileInput = document.getElementById('fileInput');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            fileLabel.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        ['dragenter', 'dragover'].forEach(eventName => {
            fileLabel.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            fileLabel.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            fileLabel.classList.add('drag-over');
        }

        function unhighlight(e) {
            fileLabel.classList.remove('drag-over');
        }

        fileLabel.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0 && files[0].type === 'application/pdf') {
                fileInput.files = files;
                fileLabel.querySelector('span').textContent = files[0].name;
            }
        }

        // Update file label when file is selected
        fileInput.addEventListener('change', function() {
            if (this.files[0]) {
                fileLabel.querySelector('span').textContent = this.files[0].name;
            }
        });

        // API Key visibility toggle
        function toggleApiKeyVisibility() {
            const apiKeyInput = document.getElementById('apiKey');
            const toggleIcon = document.getElementById('keyToggleIcon');

            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                apiKeyInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }


    </script>
</body>
</html>
