# Demo Interface Requirements (in addition to existing prototype requirements)
Flask==2.3.3
Werkzeug==2.3.7

# All existing prototype requirements are preserved:
# openai
# pandas
# pdfplumber
# pytesseract
# PyPDF2
# pdf2image
# Pillow
# tkinter (usually included with Python)
# asyncio (built-in)
# spacy (for data_extraction.py)
# google-api-python-client (for cloud_demo.py)
# google-auth (for cloud_demo.py)
