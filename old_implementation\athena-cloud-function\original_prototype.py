#cd C:\Users\<USER>\Desktop\Skunkworks\realizeanalytics-skunkworks-main\athenaPrototype; python prototype.py
import io
import os
import csv
import asyncio
import re
import base64
from typing import List, Dict, Any
import tkinter as tk
from openai import AsyncOpenAI
from tkinter import filedialog
import logging
from PIL import Image
import json

import pandas as pd
import pdfplumber
import pytesseract
from PyPDF2 import PdfReader, PdfWriter
from pdf2image import convert_from_bytes

# Global variables
RUN_TIMEOUT_SECONDS = 60  # Configurable timeout for the runs
MAX_RETRIES = 3           # Configurable maximum number of retries
DEBUGGING = False
API_KEY = "Put your API key here"

openai_client = AsyncOpenAI(api_key=API_KEY)

data_extractor_id = 'asst_AEda4VOZwyEygatil4ufeB4E'
data_extractor_instructions = """Realize Analytics - Athena
Instructions
Objective:
Athena is an AI agent specializing in processing large claim documents with two primary workflows:

Extract the explicit Room List with EXACT names and NO guessing.
Extract the explicit details (exact line items) for the Room provided to it.
Guiding Principles:

Utmost Accuracy: No assumptions, interpretations, or extrapolations. The agent only provides information exactly as stated in the documents.
Contextual Awareness: Recognizes patterns in document structure to ensure reliable parsing and extraction.
Verification-Driven: Cross-checks extracted data within the document for consistency and correctness.
Compliance and Documentation: Maintains an audit trail for all extracted data for review and validation.
Workflow 1: Room List Extraction
Purpose:
To extract the exact room names as explicitly mentioned in the document.

Step-by-Step Process:

Document Preprocessing:

Convert the document to machine-readable text if necessary (OCR processing).
Normalize text to standard fonts and formatting for easier pattern recognition.
Room List Identification:

Scan for common room-related keywords (e.g., "Room," "Living," "Bedroom," "Bathroom").
Cross-reference against standard construction and insurance terminology for validation.
Pattern Recognition:

Identify consistent formatting styles, such as bolded headers, indentation, bullet points, or table structures that define room sections.
Validate against document headings and index references (if available).
Exact Name Extraction:

Extract the room names as they appear without alteration.
Ensure there are no duplicates by comparing extracted values with existing ones.
Validation Checks:

Confirm extracted names match across multiple sections of the document if listed redundantly.
Apply data integrity checks to identify potential OCR misreads or formatting inconsistencies.
Output Formatting:

Provide the list in a structured format such as JSON or CSV for further processing.
Annotate with page numbers for reference.
Key Considerations:

Handle multi-page room listings carefully.
Address variations in naming conventions within the document.
Remove non-room related entries such as headings, footers, or descriptions.
Workflow 2: Room-Specific Line Item Extraction
Purpose:
To extract the explicit details (line items) related to a given room provided by the user.

Step-by-Step Process:

Room Selection:

Receive the room name from the user and locate it within the document.
Identify all references and section headings related to the specified room.
Line Item Detection:

Extract cost-related entries, descriptions, materials, and quantities associated with the room.
Use pattern-matching techniques to identify common layout styles (e.g., tables, bullet points, paragraph structures).
Account for abbreviations and shorthand that might refer to the same item.
Contextual Extraction:

Ensure extracted line items are within the boundaries of the selected room section.
Avoid cross-referencing with irrelevant sections to maintain accuracy.
Verification & Deduplication:

Cross-verify extracted items against other mentions within the document.
Remove duplicate entries while preserving details such as unit pricing and descriptions.
Output Structuring:

Format extracted line items into an easy-to-read tabular format.
Include page references and section headers for quick validation.
Error Handling:

Flag ambiguous or incomplete line items for manual review.
Provide warnings for potential inconsistencies (e.g., missing quantities, unclear descriptions).
Key Considerations:

Maintain line item order as found in the document.
Handle multi-line descriptions with accurate grouping.
Track labor and material costs separately where applicable.
Quality Control Measures
Cross-Validation Protocol: Regular comparison of extracted values with known templates or previous reports for accuracy assurance.
Automated Highlighting: Flagging uncertain entries for manual inspection.
Consistency Audits: Checking uniformity in terminology and numerical values across the document.
Reporting Interface: Generate summaries of extracted data, with error logs and confirmation checkpoints.
User Interaction Process
Input Stage:

User uploads claim document.
Specifies whether they need a full room list or details for a specific room.
Processing Stage:

Athena analyzes the document, following the designated workflow.
Progress status is provided with confidence scores.
Output Stage:

The structured data is presented for review in EXACT JSON

Limitations and Safeguards
No Interpretation: Athena strictly relies on the document text without extrapolation."""

# Configure logging
logging.basicConfig(
    filename="atlas.log",
    level=logging.DEBUG,
    format="%(asctime)s - %(levelname)s - %(message)s",
)

def get_file_path():
    """
    Opens a file dialog and returns the selected file path.
    """
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    file_path = filedialog.askopenfilename(title="Select a PDF file")
    return file_path

def add_base64_image(message: dict, base64_image: str, mime_type: str = 'image/png', detail: str = 'High') -> dict:
    """
    Add a base64-encoded image to an existing message object in the `content` array.
    Follows the structure:
    
    {
      "type": "image_url",
      "image_url": {
        "url": "data:<mime_type>;base64,<base64_image>",
        "detail": "<detail>"
      }
    }

    :param message: The dictionary representing the message object. 
                    Must at least have a 'content' key (array) or none.
    :param base64_image: The base64-encoded string of the image.
    :param mime_type: The MIME type, e.g. 'image/png', 'image/jpeg', etc.
    :param detail: The desired detail level, e.g. 'auto', 'low', or 'high'.
    :return: The updated message dictionary.
    """

    # Ensure the message has a 'content' list
    if 'content' not in message or not isinstance(message['content'], list):
        message['content'] = []

    # Create the image content item
    image_content = {
        "type": "image_url",
        "image_url": {
            "url": f"data:{mime_type};base64,{base64_image}",
            "detail": detail
        }
    }

    # Append the image content item to the content array
    message['content'].append(image_content)

    return message
async def add_message_and_run_agent(message, agent_instructions, vector_id = None, base64Image = None): 
    if(base64Image):
        print('Adding base64 image to message')
        response = await get_4omini_response(message, agent_instructions, base64Image)
        return response
    else:
        response = await get_4omini_response(message, agent_instructions)
        return response
    
async def add_vector_store_to_thread(vector_store_id, thread_id):
    """
    Adds a vector store to an existing thread and enables file_search.

    Args:
        vector_store_id (str): The ID of the vector store to add.
        thread_id (str): The ID of the thread to modify.
        openai_client (object): The OpenAI client object initialized in the main script.

    Returns:
        dict: The modified thread object.

    Raises:
        Exception: If the API call fails.
    """
    try:
        # Define the tool resources to enable file_search with the vector store
        tool_resources = {
            "file_search": {
                "vector_store_ids": [vector_store_id]
            }
        }

        # Make the API call to modify the thread
        modified_thread = await openai_client.beta.threads.update(
            thread_id=thread_id,
            tool_resources=tool_resources
        )

        print(f"Vector store {vector_store_id} successfully added to thread {thread_id}.")
        return modified_thread

    except Exception as e:
        raise Exception(f"Error adding vector store to thread: {e}")
    
async def send_message_to_thread(thread_id, message):
    """
    Sends a message to the assistant thread.
    """
    await openai_client.beta.threads.messages.create(
        thread_id=thread_id,
        content=message,
        role="user"
    )

async def run_assistant(assistant_id, thread_id, max_tokens=8000):
    """
    Executes an assistant thread run and retrieves the response, with timeout and retry logic.
    
    Args:
        assistant_id (str): The ID of the assistant.
        thread_id (str): The ID of the thread.
        max_tokens (int): The maximum number of tokens for the run.
    
    Returns:
        str: The assistant's response.
    """
    attempt = 0  # Tracks the current retry attempt

    while attempt < MAX_RETRIES:
        attempt += 1
        #logging.info(f"Starting run attempt {attempt} for assistant_id={assistant_id}, thread_id={thread_id}")

        try:
            # Step 1: Create a new run
            run = await openai_client.beta.threads.runs.create(
                thread_id=thread_id,
                assistant_id=assistant_id,
                max_completion_tokens=max_tokens
            )
            run_id = run.id
            #logging.info(f"Run created: run_id={run_id}, initial_status={run.status}")

            # Step 2: Monitor run status with timeout
            start_time = asyncio.get_event_loop().time()
            while run.status not in {"completed", "failed", "cancelled", "incomplete"}:
                elapsed_time = asyncio.get_event_loop().time() - start_time
                if elapsed_time > RUN_TIMEOUT_SECONDS:
                    #logging.warning(f"Run {run_id} timed out after {elapsed_time} seconds. Retrying...")
                    await cancel_run(thread_id, run_id)
                    raise TimeoutError(f"Run {run_id} exceeded timeout of {RUN_TIMEOUT_SECONDS} seconds.")
                #logging.debug(f"Run {run_id} current status: {run.status}")
                await asyncio.sleep(1)

                run = await openai_client.beta.threads.runs.retrieve(
                    run_id=run_id,
                    thread_id=thread_id
                )
                #logging.debug(f"Run status updated: run_id={run_id}, current_status={run.status}")

            # Step 3: Handle terminal states
            if run.status == "completed":
                #logging.info(f"Run {run_id} completed successfully.")
                return await fetch_run_response(thread_id, run_id)
            elif run.status == "failed":
                logging.warning(f"Run {run_id} failed. Retrying... Attempt {attempt} of {MAX_RETRIES}.")
            elif run.status == "incomplete":
                logging.warning(f"Run {run_id} incomplete. Retrying... Attempt {attempt} of {MAX_RETRIES}.")
            elif run.status == "cancelled":
                logging.warning(f"Run {run_id} cancelled. Retrying... Attempt {attempt} of {MAX_RETRIES}.")

        except TimeoutError as te:
            logging.error(f"Timeout error: {te}")
        except Exception as e:
            logging.error(f"Error during run attempt {attempt}: {e}", exc_info=True)

        # If the loop hasn't returned or raised, retry.
        #logging.info(f"Retrying run for assistant_id={assistant_id}, thread_id={thread_id}. Attempt {attempt}/{MAX_RETRIES}.")

    # If retries are exhausted, raise an exception
    raise Exception(f"Failed to complete run after {MAX_RETRIES} attempts.")
async def fetch_run_response(thread_id, run_id):
    """
    Fetches the response of a completed run and ensures it returns a simple string.
    
    Args:
        thread_id (str): The ID of the thread.
        run_id (str): The ID of the run.
    
    Returns:
        str: The content of the run's response message as a simple string.
    """
    try:
        messages = await openai_client.beta.threads.messages.list(
            thread_id=thread_id,
            run_id=run_id,
            limit=1
        )
        
        """ if messages and messages.data:
            # Extract the content from the first message
            #logging.info(f"OpenAI Message Response: {str(messages)}")
            message_content = messages.data[0].content
            #logging.info(f"Initial Output from OpenAI: {message_content}")

            # Parse message content to a simple string
            #result = parse_message_content_to_string(message_content)
            message_content = clean_string(message_content)
            result = (str(message_content).replace("\\n", "\n")  # Replace escaped newlines with actual line breaks
              .replace("[TextContentBlock(text=Text(annotations=[], value='", "")
              .replace("TextContentBlock(text=Text(annotations=[], value='", "")
              .replace("', type='text')]", "")
              .replace("', type='text')", "")
              .replace('[TextContentBlock(text=Text(annotations=[], value="', "")
              .replace('TextContentBlock(text=Text(annotations=[], value="', "")
              .replace('", type="text")]', "")
              .replace('", type="text")', ""))
            #logging.info(f"Processed Output from OpenAI: {result}")

            return result
        else:
            raise ValueError("No valid response content found in the run messages.") """
        
        if messages and messages.data:
                message_content = messages.data[0].content
                #print("Message Content Debug:", message_content)
                
                # Extract text from message content
                if message_content and isinstance(message_content, list):
                    for item in message_content:
                        if hasattr(item, "type") and item.type == "text" and hasattr(item, "text"):
                            return item.text.value.strip()
        
    except Exception as e:
        #logging.error(f"Error fetching response for run {run_id}: {e}", exc_info=True)
        raise

async def list_vector_store_files(vector_store_id):
    """
    Lists all files associated with a given vector store.
    
    Args:
        vector_store_id (str): The ID of the vector store.
        
    Returns:
        list: A list of file metadata dictionaries.
    """
    try:
        response = await openai_client.beta.vector_stores.files.list(vector_store_id=vector_store_id)
        return response["data"] if "data" in response else []
    except Exception as e:
        print(f"Error listing files for vector store {vector_store_id}: {e}")
        return []

async def cancel_run(thread_id, run_id):
    """
    Cancels a run by its thread and run ID.
    
    Args:
        thread_id (str): The ID of the thread.
        run_id (str): The ID of the run to cancel.
    """
    try:
        run = await openai_client.beta.threads.runs.cancel(
            thread_id=thread_id,
            run_id=run_id
        )
        logging.info(f"Run {run_id} cancellation initiated: status={run.status}")
    except Exception as e:
        logging.error(f"Error cancelling run {run_id}: {e}", exc_info=True)

def split_pdf_to_pages(pdf_path):
    """
    Splits the PDF into individual page byte streams.
    Returns a list of byte arrays, each representing a single-page PDF.
    """
    pages_data = []
    with open(pdf_path, 'rb') as pdf_file:
        reader = PdfReader(pdf_file)
        for page_index in range(len(reader.pages)):
            writer = PdfWriter()
            writer.add_page(reader.pages[page_index])

            buffer = io.BytesIO()
            writer.write(buffer)
            pages_data.append(buffer.getvalue())

    return pages_data

def is_text_based_page(page_data):
    """
    Determines if the page contains an embedded text layer (i.e., not scanned only).
    Returns True if text is found; otherwise False.
    """
    reader = PdfReader(io.BytesIO(page_data))
    page = reader.pages[0]
    text = page.extract_text()
    return bool(text and text.strip())

def extract_text_pdfplumber(page_data):
    """
    Extracts text from a single-page PDF using pdfplumber.
    """
    with pdfplumber.open(io.BytesIO(page_data)) as pdf:
        page = pdf.pages[0]
        return page.extract_text() or ""

def extract_text_ocr(page_data, dpi=300):
    """
    Extracts text from a scanned or image-based PDF page using OCR (Tesseract).
    Converts the PDF page to an image first, then runs OCR.
    """
    images = convert_from_bytes(page_data, dpi=dpi)
    image = images[0]
    text = pytesseract.image_to_string(image)
    return text or ""

def clean_text(text):
    """
    Cleans the extracted text by removing extra whitespace and non-printable characters.
    """
    text = re.sub(r'\s+', ' ', text)
    text = ''.join(c for c in text if c.isprintable())
    return text.strip()

async def get_o1_response(prompt, agent_instructions):
    completion = await openai_client.chat.completions.create(
    model="o3-mini",
    messages=[
        {"role": "assistant", "content": agent_instructions},
        {"role": "user", "content": prompt}
    ]
    )
    #print(completion)
    message = completion.choices[0].message.content
    print(f"o1 response: {message}")
    return message

async def get_4omini_response(prompt, agent_instructions, base64_image): 
    """ 
    Generates a response from the 4o-mini model with a Base64 image attachment. 
    :param prompt: The user prompt as a string. 
    :param agent_instructions: Instructions for the assistant as a string. 
    :param base64_image: The Base64-encoded image string (without the data URI prefix). 
    :return: The assistant's response message. """ 

    # Prepare the image data with the appropriate data URI prefix 
    image_data = f"data:image/png;base64,{base64_image}" 

    # Construct the messages with both text and image content 
    messages = [ {"role": "assistant", "content": agent_instructions}, { "role": "user", "content": [ {"type": "text", "text": prompt}, {"type": "image_url", "image_url": {"url": image_data}} ] } ]

    # Make the API call to OpenAI 
    completion = await openai_client.chat.completions.create( model="gpt-4o-mini", messages=messages ) 
    # Extract and return the assistant's response
    message = completion.choices[0].message.content 
    print(f"4o Mini response: {message}") 
    return message

async def get_4omini_response_archive(prompt, agent_instructions, base64_image, max_tokens=16384):
    """
    Generates a response from the 4o-mini model with a Base64 image attachment.

    :param prompt: The user prompt as a string.
    :param agent_instructions: Instructions for the assistant as a string.
    :param base64_image: The Base64-encoded image string (without the data URI prefix).
    :param max_tokens: The maximum number of tokens to generate in the response.
    :return: The assistant's response message.
    """
    # Prepare the image data with the appropriate data URI prefix
    image_data = f"data:image/png;base64,{base64_image}"

    # Construct the messages with both text and image content
    messages = [
        {"role": "assistant", "content": agent_instructions},
        {
            "role": "user",
            "content": [
                {"type": "text", "text": prompt},
                {
                    "type": "image_url",
                    "image_url": {
                        "url": image_data
                    }
                }
            ]
        }
    ]

     # Make the API call to OpenAI with the max_tokens parameter
    completion = await openai_client.chat.completions.create(
        model="gpt-4o-mini",
        messages=messages,
        max_tokens=max_tokens
    )

    # Extract and return the assistant's response
    message = completion.choices[0].message.content
    print(f"4o Mini response: {message}")
    return message


def extract_json_line_items(input_string):
    """
    Extracts a JSON object from the input string and returns it as a Python dictionary.

    Args:
        input_string (str): The string containing the JSON object.

    Returns:
        dict: The extracted JSON object.

    Raises:
        ValueError: If no JSON object is found or if the JSON is malformed.
    """
    
    # Find the first '{'
    start = input_string.find('{')
    if start == -1:
        raise ValueError("No JSON object found in the input string.")

    brace_count = 0
    end = start
    in_string = False
    escape = False

    for i, char in enumerate(input_string[start:], start=start):
        if char == '"' and not escape:
            in_string = not in_string
        if not in_string:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end = i + 1
                    break
        if char == '\\' and not escape:
            escape = True
        else:
            escape = False
    else:
        raise ValueError("No matching closing brace found for JSON object.")

    json_str = input_string[start:end]

    try:
        json_obj = json.loads(json_str)
    except json.JSONDecodeError as e:
        raise ValueError(f"Error parsing JSON: {e}")

    return json_obj

def pdf_page_to_base64(page_data: bytes, dpi: int = 150) -> str:
    """
    Converts a single PDF page (in bytes) to a base64-encoded PNG image.
    
    Args:
        page_data (bytes): Byte content of a single-page PDF.
        dpi (int): Dots-per-inch resolution for rendering; default is 150.

    Returns:
        str: Base64-encoded PNG image as a string.
    """
    # Convert PDF page bytes to PIL Images (one PDF page -> possibly multiple images, but typically one)
    images = convert_from_bytes(page_data, dpi=dpi)
    if not images:
        raise ValueError("Failed to render PDF page into an image.")

    # Take the first image (should represent this single PDF page)
    image = images[0]

    # Save the image in a buffer as PNG
    buffer = io.BytesIO()
    image.save(buffer, format="PNG")

    # Convert the image bytes to a Base64-encoded string
    encoded_string = base64.b64encode(buffer.getvalue()).decode("utf-8")

    # 5. Write the encoded string to 'base64.txt' in the root directory.
    with open("base64.txt", "w", encoding="utf-8") as file:
        file.write(encoded_string)

    return encoded_string

def pdf_page_to_base64_archive(page_data: bytes, dpi: int = 150) -> str:
    """
    Converts a single PDF page (in bytes) to a base64-encoded PNG image.
    
    Args:
        page_data (bytes): Byte content of a single-page PDF.
        dpi (int): Dots-per-inch resolution for rendering; default is 150.

    Returns:
        str: Base64-encoded PNG image as a string.
    """
    # 1. Convert the PDF bytes to a list of PIL Images.
    #    Since it's a single-page PDF, we'll take the first image in the list.
    images = convert_from_bytes(page_data, dpi=dpi)
    if not images:
        raise ValueError("Failed to render PDF page into an image.")

    # 2. Take the first image (this should be your single PDF page).
    image = images[0]

    # 3. Save the image into an in-memory buffer as PNG.
    buffer = io.BytesIO()
    image.save(buffer, format="PNG")

    # 4. Convert the bytes to a base64-encoded string.
    encoded_string = base64.b64encode(buffer.getvalue()).decode("utf-8")

    # 5. Write the encoded string to 'base64.txt' in the root directory.
    with open("base64.txt", "w", encoding="utf-8") as file:
        file.write(encoded_string)

    return encoded_string
async def process_pdf(pdf_path, csv_filepath='Text output.csv'):
    """
    Splits the PDF into pages, determines which extraction method (text-based vs OCR) to use,
    extracts all text, cleans it, and returns a DataFrame with one row per page.
    """
    pages_data = split_pdf_to_pages(pdf_path)
    all_pages_text = []

    prompt = """Your job is to comb through the OCR extracted text and extract ALL of the line items. The OCR is imperfect.

        You must extract and provide ONLY the room line items in PRECISE JSON. Provide no text outside the JSON or the process will fail.
        DO NOT GUESS on items like Room Name if the formatting seems to have messed up the OCR. Instead put all the possible ones in a list like "<room_1>_and_<room_2>_possible"
        Under NO circumstances should numbers ever contain commas, as this will break it. The only change allowed to the extracted values are changes which allow the data to be parsed correctly as JSON fields.
        This is for a major client and UTTER ACCURACY is required. Please take your time and really try to do your best.
        The structure of the JSON the intern makes must be (With EXACT naming conventions and EXACT extraction):
        {
            "room_name": {  // The name of the primary room (e.g., "Living Room", "Kitchen"). Remove any additions to it which don't fit and seem like erroneous additions from the OCR. This room will be the room in the hierarchy above the subroom (if there is one). It will be in the header of the line item section. Under NO circumstances should you put 'room_name' in this field... you MUST use the correct room name instead.
                "subroom": {
                "subroom_name": {  // The name of the subroom, typically the same as the base room (e.g., "Living Room"). Under NO circumstances can this be blank or just the field name.  Under NO circumstances should you put 'subroom_name' in this field... you MUST use the correct room name instead. 
                    "square_footage_floors": 0,  // Numeric value representing the total square footage subroom FLOORING. Ensure you use SQUARE FOOTAGE and DO NOT GUESS. If the page doesn't have the SF then just leave it blank.
                    "square_footage_walls": 0,  // Numeric value representing the total square footage subroom WALLS. Ensure you use SQUARE FOOTAGE and DO NOT GUESS. If the page doesn't have the SF then just leave it blank.
                    "line_items": [
                    {
                        "line_item_number": "Enter the line item number here IF IT EXISTS. If not, just leave it blank. If it exists, use it to help ensure no line items are missed in the sequence",
                        "description": "Enter item/service description here",  // Extract the line item description verbatim if possible and ensure ALL the text describing what the line item costs are for are included verbatim (do not change or add words)
                        "quantity": 0.00,  // Numeric value indicating the quantity of the item/service. Ensure this is accurate as accuracy of this is necessary for scoping (e.g. disputes on the total square footage if that is the quantity)
                        "cost_total": 0.00,  // Total cost, including tax and other applicable charges for that specific line item. it should always have a value and should be the largest.
                        "cost_description": "Enter how the cost total is derived and the type of service. Explain this like to an adjuster. It should be in natural language. Update and optimize any prior description.",
                        "notes": "Provide any notes which would be important for the insurance adjuster to know or potential issues with the extraction or things you aren't sure of for the later members of the team who will use this with the images to finalize the extraction."
                    }
                    ]
                }
                }
            }
        }

        Note that it is VITAL that you account for and format ALL line items in the text. Miss nothing or this will cause reputational harm to our company.

        There will often be Line Item Index numbers but not always. When they exist, use them to cross match to ensure none are missed.
        The OCR extracted text for you to analyze is: 
            """
        
     # Define CSV headers based on the JSON template
    
    prompt_confirm = """Your job is to take the first pass at data extraction and complete it by extracting ALL of the line items. Ensure NOTHING is missed by referencing it against the attached image of the page.

    The person doing the first pass is an intern who often gets line items or room names/subrooms wrong. Do whatever you can do ensure the output is perfect to help us avoid reputational harm.
    This is for a major client and UTTER ACCURACY is required. Please take your time and really try to do your best.
        You must extract and provide ONLY the room line items in PRECISE JSON. Provide no text outside the JSON or the process will fail.
        The first pass was an intern who used OCR and that will often result in the wrong room name sometimes being inserted for certain line items. PLEASE MAKE SURE you focus on correcting this especially. The formatting of the PDF will very frequently cause misattributions.
        Under NO circumstances should numbers ever contain commas, as this will break it. The only change allowed to the extracted values are changes which allow the data to be parsed correctly as JSON fields.

        The structure of the JSON the intern makes must be (With EXACT naming conventions and EXACT extraction):
        {
            "room_name": {  // The name of the primary room (e.g., "Living Room", "Kitchen"). Remove any additions to it which don't fit and seem like erroneous additions from the OCR. This room will be the room in the hierarchy above the subroom (if there is one). It will be in the header of the line item section. Under NO circumstances should you put 'room_name' in this field... you MUST use the correct room name instead.
                "subroom": {
                "subroom_name": {  // The name of the subroom, typically the same as the base room (e.g., "Living Room"). Under NO circumstances can this be blank or just the field name.  Under NO circumstances should you put 'subroom_name' in this field... you MUST use the correct room name instead. 
                    "square_footage_floors": 0,  // Numeric value representing the total square footage subroom FLOORING. Ensure you use SQUARE FOOTAGE and DO NOT GUESS. If the page doesn't have the SF then just leave it blank.
                    "square_footage_walls": 0,  // Numeric value representing the total square footage subroom WALLS. Ensure you use SQUARE FOOTAGE and DO NOT GUESS. If the page doesn't have the SF then just leave it blank.
                    "line_items": [
                    {
                        "line_item_number": "Enter the line item number here IF IT EXISTS. If not, just leave it blank. If it exists, use it to help ensure no line items are missed in the sequence",
                        "description": "Enter item/service description here",  // Extract the line item description verbatim if possible and ensure ALL the text describing what the line item costs are for are included verbatim (do not change or add words)
                        "quantity": 0.00,  // Numeric value indicating the quantity of the item/service. Ensure this is accurate as accuracy of this is necessary for scoping (e.g. disputes on the total square footage if that is the quantity)
                        "cost_total": 0.00,  // Total cost, including tax and other applicable charges for that specific line item. it should always have a value and should be the largest.
                        "cost_description": "Enter how the cost total is derived and the type of service. Explain this like to an adjuster. It should be in natural language. Update and optimize any prior description.",
                        "notes": "Provide any notes which would be important for the insurance adjuster to know or potential issues with the extraction or things you aren't sure of. Rewrite this each run based on your most current notes. Be sure to use this fieldf extensively to explain anything someone later in the flow should confirm or not... err on the side of caution."
                    }
                    ]
                }
                }
            }
        }

        Note that it is VITAL that you account for and format ALL line items in the image. Miss nothing or this will cause reputational harm to our company.

        There will often be Line Item Index numbers but not always. When they exist, use them to cross match to ensure none are missed.

        
        Note that you should rewrite whatever is necessary to ensure this is accurate. The attempt below likely has errors and any errors would damage our reputation.
        
        The first pass at the extracted text for you to complete MISSING NOTHING is: 
            """
    #NOTE THAT IF YOU CANNOT SEE OR ACCESS THE BASE 64 ENCODED IMAGE RETURN ONLY THE MESSAGE 'No Image to Process' EXACTLY. It should be provided in the image url though.

    prompt_grade = """
    Our company had a new intern take a pass at extracting the line items from a claim page, but they often do it wrong. Can you double check their results and ensure all the values are in the right columns and the room names are correct? They often use the wrong columns for the numbers though usually get the numbers themselves right.
        Outline ONLY the issues and mistakes you found in a bulletpoint list and tell the intern how to correct it. Be hyper detailed and exact.
        NEVER UNDER ANY CIRCUMSTANCES DO YOU SUGGEST ADDING FIELDS (e.g. Total) TO THE JSON BELOW. ANY DEVIATION FROM THE STRUCTURE WILL CAUSE THE PROCESS TO FAIL!
        This quality check is vital to protect our company's reputation and you take great pride in not missing anything.
        The intern frequently puts the values in the wrong columns so make sure you review the table closely... they can have odd formatting which causes confusion. We frequently see the columns get shifted in their work so there is no total but the total is in the tax line etc.
        The intern also frequently uses the wrong room name, but uses one which DOES appear on the page. Usually the page name is obvious in the picture so ALWAYS double check this. Additionally, rooms often has subrooms which should be noted.
        Be hyper paranoid and ensure you check everything.
        If there is no object passed to you and the page doesn't have line items simply say 'The page has no line items.'
        This is for a major client and UTTER ACCURACY is required. Please take your time and really try to do your best.
        Ensure the notes are ENTIRELY SPECIFIC TO THIS SPECIFIC PAGE!!!  Avoid adding ANYTHING which isn't requested or overbaking the suggestions.
        
        The structure of the JSON the intern makes must be (With EXACT naming conventions and EXACT extraction):
        {
            "room_name": {  // The name of the primary room (e.g., "Living Room", "Kitchen"). Remove any additions to it which don't fit and seem like erroneous additions from the OCR. This room will be the room in the hierarchy above the subroom (if there is one). It will be in the header of the line item section. Under NO circumstances should you put 'room_name' in this field... you MUST use the correct room name instead.
                "subroom": {
                "subroom_name": {  // The name of the subroom, typically the same as the base room (e.g., "Living Room"). Under NO circumstances can this be blank or just the field name.  Under NO circumstances should you put 'subroom_name' in this field... you MUST use the correct room name instead.
                    "square_footage_floors": 0,  // Numeric value representing the total square footage subroom FLOORING. Ensure you use SQUARE FOOTAGE and DO NOT GUESS. If the page doesn't have the SF then just leave it blank.
                    "square_footage_walls": 0,  // Numeric value representing the total square footage subroom WALLS. Ensure you use SQUARE FOOTAGE and DO NOT GUESS. If the page doesn't have the SF then just leave it blank.
                    "line_items": [
                    {
                        "line_item_number": "Enter the line item number here IF IT EXISTS. If not, just leave it blank. If it exists, use it to help ensure no line items are missed in the sequence",
                        "description": "Enter item/service description here",  // Extract the line item description verbatim if possible and ensure ALL the text describing what the line item costs are for are included verbatim (do not change or add words)
                        "quantity": 0.00,  // Numeric value indicating the quantity of the item/service. Ensure this is accurate as accuracy of this is necessary for scoping (e.g. disputes on the total square footage if that is the quantity)
                        "cost_total": 0.00,  // Total cost, including tax and other applicable charges for that specific line item. it should always have a value and should be the largest.
                        "cost_description": "Enter how the cost total is derived and the type of service. Explain this like to an adjuster. It should be in natural language. Update and optimize any prior description.",
                        "notes": "Provide any notes which would be important for the insurance adjuster to know or potential issues with the extraction or things you aren't sure of. Rewrite this each run based on your most current notes. Be sure to use this fieldf extensively to explain anything someone later in the flow should confirm or not... err on the side of caution."
                    }
                    ]
                }
                }
            }
        }

        The intern's pass at the extracted text for you to help ensure we are MISSING NOTHING is: 
            """

    prompt_correct = """Our company had a new intern take a pass at extracting the line items from a claim page, but they often do it wrong. Can you double check their results and ensure all the values are in the right columns and the room names are correct? They often use the wrong columns for the numbers though usually get the numbers themselves right.

        You must extract and provide ONLY the room line items in PRECISE JSON. Provide no text outside the JSON or the process will fail.
        This is for a major client and UTTER ACCURACY is required. Please take your time and really try to do your best.
        Under NO circumstances should numbers ever contain commas, as this will break it. The only change allowed to the extracted values are changes which allow the data to be parsed correctly as JSON fields.

        The structure of the JSON the intern makes must be (With EXACT naming conventions and EXACT extraction):
        {
            "room_name": {  // The name of the primary room (e.g., "Living Room", "Kitchen"). Remove any additions to it which don't fit and seem like erroneous additions from the OCR. This room will be the room in the hierarchy above the subroom (if there is one). It will be in the header of the line item section. Under NO circumstances should you put 'room_name' in this field... you MUST use the correct room name instead.
                "subroom": {
                "subroom_name": {  // The name of the subroom, typically the same as the base room (e.g., "Living Room"). Under NO circumstances can this be blank or just the field name.  Under NO circumstances should you put 'subroom_name' in this field... you MUST use the correct room name instead. 
                    "square_footage_floors": 0,  // Numeric value representing the total square footage subroom FLOORING. Ensure you use SQUARE FOOTAGE and DO NOT GUESS. If the page doesn't have the SF then just leave it blank.
                    "square_footage_walls": 0,  // Numeric value representing the total square footage subroom WALLS. Ensure you use SQUARE FOOTAGE and DO NOT GUESS. If the page doesn't have the SF then just leave it blank.
                    "line_items": [
                    {
                        "line_item_number": "Enter the line item number here IF IT EXISTS. If not, just leave it blank. If it exists, use it to help ensure no line items are missed in the sequence",
                        "description": "Enter item/service description here",  // Extract the line item description verbatim if possible and ensure ALL the text describing what the line item costs are for are included verbatim (do not change or add words)
                        "quantity": 0.00,  // Numeric value indicating the quantity of the item/service. Ensure this is accurate as accuracy of this is necessary for scoping (e.g. disputes on the total square footage if that is the quantity)
                        "cost_total": 0.00,  // Total cost, including tax and other applicable charges for that specific line item. it should always have a value and should be the largest.
                        "cost_description": "Enter how the cost total is derived and the type of service. Explain this like to an adjuster. It should be in natural language. Update and optimize any prior description.",
                        "notes": "Provide any notes which would be important for the insurance adjuster to know or potential issues with the extraction or things you aren't sure of. Rewrite this each run based on your most current notes. Be sure to use this fieldf extensively to explain anything someone later in the flow should confirm or not... err on the side of caution."
                    }
                    ]
                }
                }
            }
        }

        Note that it is VITAL that you account for and format ALL line items in the image. Miss nothing or this will cause reputational harm to our company.

        There will often be Line Item Index numbers but not always. When they exist, use them to cross match to ensure none are missed.

        Fully rewrite it if necessary to ensure accuracy.

        Note that you should rewrite whatever is necessary to ensure this is accurate. The attempt below likely has errors and any errors would damage our reputation.

            """
        
     # Define CSV headers based on the JSON template
       
    headers = [
        'page_number',
        'line_item_number',
        'room_name',
        'subroom_name',
        'square_footage_floors',
        'square_footage_walls',
        'description',
        'quantity',
        'cost_total',
        'cost_description',
        'notes'
    ]

    # Check if the CSV file exists; if not, create it with headers.
    if not os.path.isfile(csv_filepath):
        with open(csv_filepath, mode='w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()

    # Create a lock so only one coroutine writes at a time
    csv_lock = asyncio.Lock()

    # Process each page
    for page_num, page_data in enumerate(pages_data, start=1):
        print(f"Processing page {page_num}...")
        try:
            # Decide extraction method
            if is_text_based_page(page_data):
                raw_text = extract_text_pdfplumber(page_data)
            else:
                raw_text = extract_text_ocr(page_data)

            cleaned = clean_text(raw_text)
            base64_image = pdf_page_to_base64(page_data)

            # Ask your LLM to extract data as JSON
            #data_json_raw = await add_message_and_run_agent(f"{prompt}{cleaned}", data_extractor_id)
            data_json_raw = await get_o1_response(f"{prompt}{cleaned}", data_extractor_instructions)
            data_json = extract_json_line_items(data_json_raw)

            print(f"First Pass: {data_json}")

            data_json_raw = await add_message_and_run_agent(f"{prompt_confirm}{data_json}", data_extractor_instructions, None, base64_image)
            data_json = extract_json_line_items(data_json_raw)
            print(f"Second Pass: {data_json}")
            grade = await add_message_and_run_agent(f"{prompt_grade}{data_json}", data_extractor_instructions, None, base64_image)
            print(f"Grade: {grade}")

            data_json_raw = await add_message_and_run_agent(f"{prompt_correct}\n\nThe notes to help you correct it are: {grade}\n\nThe intern's pass at the extracted text for you to complete (even if you need to significantly rewrite it) MISSING NOTHING is: {data_json}", data_extractor_instructions, None, base64_image)
            data_json = extract_json_line_items(data_json_raw)

            print(f"Third Pass: {data_json}")

            for room_name, room_info in data_json.items():
                subrooms = room_info.get("subroom", {})
                print(subrooms)
                for subroom_name, subroom_data in subrooms.items():
                    print(f"{subroom_name}: {subroom_data}")
                    square_footage_floors = subroom_data.get("square_footage_floors", 0)
                    square_footage_walls = subroom_data.get("square_footage_walls", 0)
                    line_items = subroom_data.get("line_items", [])
                    for item in line_items:
                        row = {
                            'page_number':      page_num,
                            'line_item_number':      item.get("line_item_number", 0),
                            'room_name':      room_name,
                            'subroom_name':   subroom_name,
                            'square_footage_floors':   square_footage_floors,
                            'square_footage_walls':   square_footage_walls,
                            'description':    item.get("description", ""),
                            'quantity':            item.get("quantity", 0.00),
                            'cost_total':          item.get("cost_total", 0.00),
                            'cost_description':          item.get("cost_description", ""),
                            'notes':          item.get("notes", "")
                        }

                        # Write immediately to CSV (thread-safe with csv_lock)
                        async with csv_lock:
                            with open(csv_filepath, mode='a', newline='', encoding='utf-8') as csvfile:
                                writer = csv.DictWriter(csvfile, fieldnames=headers)
                                writer.writerow(row)

            # Store summarized info about this page
            all_pages_text.append({
                "page_number": page_num,
                "extracted_text": data_json
            })

        except Exception as e:
            print(f"Error processing page {page_num}: {e}")
            all_pages_text.append({
                "page_number": page_num,
                "extracted_text": ""
            })

    # Return a DataFrame summarizing the JSON extracted from all pages
    return pd.DataFrame(all_pages_text)

async def main():
    pdf_path = get_file_path()
    #pdf_path = 'Test Input.pdf'
    if pdf_path:
        df = await process_pdf(pdf_path, "Test output.csv")
        print(df)
        # Export to CSV
        df.to_csv("extracted_jsons.csv", index=False)

if __name__ == "__main__":
    asyncio.run(main())