/* Athena Demo Interface Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header {
    text-align: center;
    color: white;
    margin-bottom: 30px;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.header p {
    font-size: 1.2rem;
    opacity: 0.9;
}

/* Upload Section */
.upload-section {
    margin-bottom: 30px;
}

.upload-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
}

.upload-card h2 {
    margin-bottom: 25px;
    color: #333;
    font-size: 1.8rem;
}

.file-upload {
    margin-bottom: 25px;
}

.file-upload input[type="file"] {
    display: none;
}

.file-label {
    display: block;
    padding: 40px 20px;
    border: 3px dashed #ddd;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f8f9fa;
}

.file-label:hover, .file-label.drag-over {
    border-color: #667eea;
    background: #f0f4ff;
    transform: translateY(-2px);
}

.file-label i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 15px;
    display: block;
}

.file-label span {
    font-size: 1.1rem;
    color: #666;
}

/* API Key Section */
.api-key-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 2px solid #e9ecef;
}

.api-key-section .control-group {
    position: relative;
    display: flex;
    align-items: center;
    gap: 10px;
}

.api-key-section .full-width {
    width: 100%;
}

.api-key-section input[type="password"],
.api-key-section input[type="text"] {
    flex: 1;
    padding: 12px 50px 12px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    font-family: 'Courier New', monospace;
    background: white;
    transition: border-color 0.3s ease;
}

.api-key-section input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.btn-toggle-key {
    position: absolute;
    right: 10px;
    background: none;
    border: none;
    color: #666;
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: color 0.3s ease;
}

.btn-toggle-key:hover {
    color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.api-key-help {
    margin-top: 8px;
    text-align: center;
}

.api-key-help small {
    color: #666;
    font-size: 0.85rem;
}

.api-key-help a {
    color: #667eea;
    text-decoration: none;
}

.api-key-help a:hover {
    text-decoration: underline;
}

.controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 20px;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-group label {
    font-weight: 600;
    color: #555;
}

.control-group select {
    padding: 8px 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-weight: 600;
    color: #555;
}

.checkbox-label input[type="checkbox"] {
    margin-right: 8px;
}

/* Buttons */
.btn-primary, .btn-secondary {
    padding: 12px 25px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

/* Processing Section */
.processing-section {
    margin-bottom: 30px;
}

.progress-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
}

.progress-card h3 {
    margin-bottom: 20px;
    color: #333;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 20px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.status-info p {
    margin: 5px 0;
    font-size: 1.1rem;
    color: #666;
}

/* Results Section */
.results-section {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.results-header h2 {
    color: #333;
    font-size: 1.8rem;
}

.results-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.summary-stats {
    display: flex;
    justify-content: space-around;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    min-width: 120px;
    flex: 1;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
}

/* Results Table */
.results-table-container {
    overflow-x: auto;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.results-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
}

.results-table th {
    background: #f8f9fa;
    padding: 15px 10px;
    text-align: left;
    font-weight: 600;
    color: #555;
    border-bottom: 2px solid #dee2e6;
}

.results-table td {
    padding: 12px 10px;
    border-bottom: 1px solid #dee2e6;
    vertical-align: top;
}

.results-table tr:hover {
    background: #f8f9fa;
}

/* Error Section */
.error-section {
    margin-bottom: 30px;
}

.error-card {
    background: white;
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    text-align: center;
    border-left: 5px solid #dc3545;
}

.error-card h3 {
    color: #dc3545;
    margin-bottom: 15px;
}

.error-card p {
    color: #666;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .controls {
        flex-direction: column;
        align-items: stretch;
    }

    .results-header {
        flex-direction: column;
        align-items: stretch;
    }

    .results-controls {
        justify-content: center;
    }

    .summary-stats {
        flex-direction: column;
    }

    .results-table {
        font-size: 0.9rem;
    }

    .results-table th,
    .results-table td {
        padding: 8px 6px;
    }
}

/* Loading Animation */
@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.processing-section .progress-card {
    animation: pulse 2s infinite;
}
