#!/bin/bash

echo "========================================"
echo "   ATHENA DEMO INTERFACE LAUNCHER"
echo "========================================"
echo ""
echo "Starting Athena Demo..."
echo ""

# Check if Python is available
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "ERROR: Python is not installed or not in PATH"
        echo "Please install Python 3.7+ and try again"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Check Python version
PYTHON_VERSION=$($PYTHON_CMD --version 2>&1 | awk '{print $2}')
echo "Using Python $PYTHON_VERSION"

# Install Flask if not present
$PYTHON_CMD -c "import flask" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Installing Flask for demo interface..."
    $PYTHON_CMD -m pip install Flask==2.3.3 Werkzeug==2.3.7
fi

# Start the demo
echo ""
echo "Opening Athena Demo Interface..."
echo "Browser will open automatically at http://localhost:5000"
echo ""
echo "Press Ctrl+C to stop the demo"
echo ""

$PYTHON_CMD start_demo.py
