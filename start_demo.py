#!/usr/bin/env python3
"""
Athena Demo Startup Script
Provides easy startup options for both demo and original interfaces
"""

import os
import sys
import subprocess
import webbrowser
import time

def check_requirements():
    """Check if Flask is installed for demo interface"""
    try:
        import flask
        return True
    except ImportError:
        return False

def install_demo_requirements():
    """Install demo requirements"""
    print("Installing demo interface requirements...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Flask==2.3.3", "Werkzeug==2.3.7"])
        return True
    except subprocess.CalledProcessError:
        return False

def start_demo_interface():
    """Start the web demo interface"""
    print("\n" + "="*60)
    print("🚀 STARTING ATHENA DEMO INTERFACE")
    print("="*60)
    print("✅ Original prototype.py functionality preserved")
    print("✅ Web interface provides user-friendly demo")
    print("✅ Batch processing with user-configurable limits")
    print("✅ GPT-4.1 model support available")
    print("="*60)
    
    # Check and install requirements if needed
    if not check_requirements():
        print("📦 Installing demo requirements...")
        if not install_demo_requirements():
            print("❌ Failed to install requirements. Please run:")
            print("   pip install Flask==2.3.3 Werkzeug==2.3.7")
            return False
    
    # Set environment for GPT-4.1 if desired
    use_gpt41 = input("\n🤖 Use GPT-4.1 model? (y/N): ").lower().strip()
    if use_gpt41 == 'y':
        os.environ['USE_GPT41'] = 'true'
        print("✅ GPT-4.1 model enabled")
    else:
        print("✅ Using GPT-4o-mini (default)")
    
    print("\n🌐 Starting web server...")
    print("📱 Demo will open at: http://localhost:5000")
    print("⏹️  Press Ctrl+C to stop the demo")
    print("\n" + "="*60)
    
    # Start the demo interface
    try:
        # Open browser after a short delay
        import threading
        def open_browser():
            time.sleep(2)
            webbrowser.open('http://localhost:5000')
        
        browser_thread = threading.Thread(target=open_browser)
        browser_thread.daemon = True
        browser_thread.start()
        
        # Import and run demo interface
        import demo_interface
        demo_interface.app.run(debug=False, host='0.0.0.0', port=5000)
        
    except KeyboardInterrupt:
        print("\n\n✅ Demo stopped successfully")
        return True
    except Exception as e:
        print(f"\n❌ Error starting demo: {e}")
        return False

def start_original_prototype():
    """Start the original command-line prototype"""
    print("\n" + "="*60)
    print("🔧 STARTING ORIGINAL PROTOTYPE")
    print("="*60)
    print("✅ Command-line interface")
    print("✅ All original functionality preserved")
    print("✅ Direct file dialog selection")
    print("="*60)
    
    try:
        import asyncio
        import prototype
        asyncio.run(prototype.main())
    except Exception as e:
        print(f"❌ Error running prototype: {e}")
        return False

def main():
    """Main startup menu"""
    print("\n" + "="*80)
    print("🏛️  ATHENA - INSURANCE CLAIM DOCUMENT PROCESSOR")
    print("="*80)
    print("Choose your interface:")
    print()
    print("1. 🌐 Web Demo Interface (Recommended for demos)")
    print("   - User-friendly web interface")
    print("   - Batch processing controls")
    print("   - Real-time progress tracking")
    print("   - Professional results display")
    print("   - GPT-4.1 model option")
    print()
    print("2. 🔧 Original Prototype (Command-line)")
    print("   - Direct command-line interface")
    print("   - All original functionality")
    print("   - Technical user interface")
    print()
    print("3. ❌ Exit")
    print("="*80)
    
    while True:
        choice = input("\nSelect option (1-3): ").strip()
        
        if choice == '1':
            if start_demo_interface():
                break
        elif choice == '2':
            if start_original_prototype():
                break
        elif choice == '3':
            print("👋 Goodbye!")
            break
        else:
            print("❌ Invalid choice. Please select 1, 2, or 3.")

if __name__ == "__main__":
    main()
