import asyncio
import tkinter as tk
from tkinter import filedialog
from openai import Async<PERSON>penAI
from openai import OpenAI
from email.mime.text import MIMEText
from smtplib import SMTP
import csv
import logging
import traceback
import os
import PyPDF2
import tempfile
import json
from collections import defaultdict
import re
import spacy
import pdfplumber
from typing import List, Dict, Pattern

openai_client = AsyncOpenAI(api_key="********************************************************************************************************************************************************************")

data_extractor_id = 'asst_AEda4VOZwyEygatil4ufeB4E'

deepseek_key = "***********************************"

#provider = "OpenRouter"
client = OpenAI(api_key=deepseek_key, base_url="https://api.deepseek.com")
# Initialize OpenAI client
#if provider == "OpenRouter":
    #client = OpenAI(
    #    base_url="https://openrouter.ai/api/v1",
    #    api_key="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",
    #)


# Configure logging
logging.basicConfig(
    filename="atlas.log",
    level=logging.DEBUG,
    format="%(asctime)s - %(levelname)s - %(message)s",
)

# Global variables
RUN_TIMEOUT_SECONDS = 60  # Configurable timeout for the runs
MAX_RETRIES = 3           # Configurable maximum number of retries
DEBUGGING = False

# 1) LOAD ROOMS FROM JSON
def load_rooms_from_json(file_path: str = "rooms.json") -> list:
    """
    Loads a list of rooms from a local JSON file containing a top-level 'rooms' key.
    """
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"Could not find the specified JSON file: {file_path}")

    with open(file_path, "r", encoding="utf-8") as f:
        data = json.load(f)

    return data.get("rooms", [])


# 2) BUILD A REGEX PATTERN FROM THE LOADED ROOMS
def compile_room_regex_archive(rooms_list: list) -> re.Pattern:
    """
    Creates a compiled regex pattern that captures:
      - optional directional modifiers (front, rear, left, right, etc.)
      - any recognized room from 'rooms_list'
      - optional trailing digits (for forms like 'Bedroom 1')
    """
    # Escape each room so that special chars/spaces won't break the pattern
    escaped_rooms = [re.escape(r) for r in rooms_list]

    # We also include fallback keywords: 'room', 'bedroom', etc. if needed
    # But you can remove these if your JSON already covers them comprehensively.
    fallback_terms = ["room", "bedroom"]
    all_terms = escaped_rooms + fallback_terms

    # Construct the big OR group
    terms_pattern = "|".join(all_terms)

    # Optional direction or descriptor (front, rear, left, right, north, etc.)
    direction_pattern = r"(front|rear|left|right|north|south|east|west)?\s*"

    # The final pattern (case-insensitive):
    #  (?i) : case-insensitive
    #  \b : word boundary (start)
    #  (directional pattern) + (our known term from the dictionary) + optional number
    #  e.g. "right front bedroom 1"
    pattern = rf"(?i)\b{direction_pattern}({terms_pattern})(\s*\d+)?\b"

    return re.compile(pattern, re.IGNORECASE)

def compile_room_regex(rooms_list: List[str]) -> re.Pattern:
    """
    Compiles a regex pattern that matches exact room names and the word 'room'.
    
    :param rooms_list: List of room names (e.g., ["Bedroom", "Kitchen"]).
    :return: Compiled regex pattern.
    """
    # Escape each room to handle special characters and spaces
    escaped_rooms = [re.escape(room) for room in rooms_list]
    
    # Include the standalone word 'room'
    all_terms = escaped_rooms + [r'room']
    
    # Join terms with OR operator and enforce word boundaries
    pattern = r'\b(?:' + '|'.join(all_terms) + r')\b'
    
    return re.compile(pattern, re.IGNORECASE)

def process_pdf_parallel(
    pdf_path: str,
    rooms_list: List[str],
    compiled_regex: Pattern,
    context_length: int = 300,
    n_process: int = 4,
    batch_size: int = 20
) -> List[Dict]:
    """
    Reads the PDF with pdfplumber, then processes pages in parallel using spaCy's nlp.pipe.
    This approach can speed up token-based analysis for multi-page PDFs by distributing the
    CPU-bound NLP tasks across multiple processes or threads.

    :param pdf_path: Path to the PDF file to process.
    :param rooms_list: List of known room strings.
    :param compiled_regex: Pre-compiled regex pattern for capturing numeric/directional variants.
    :param context_length: Number of characters from page text to store as snippet.
    :param n_process: Number of processes for spaCy's pipe concurrency.
    :param batch_size: How many pages to send in each batch to spaCy's pipe.
    :return: A list of dicts, each with 'room', 'page', 'context_snippet'.
    """
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    # Load spaCy model once
    nlp = spacy.load("en_core_web_sm")

    # Convert known rooms to lowercase set for direct matching
    known_rooms_lower = {r.lower() for r in rooms_list}

    # Extract all relevant page texts first
    pages_text = []
    with pdfplumber.open(pdf_path) as pdf:
        for page_index, page in enumerate(pdf.pages, start=1):
            raw_text = page.extract_text() or ""
            if raw_text.strip():
                # Store the text plus metadata so we can later match doc -> page
                pages_text.append((page_index, raw_text, raw_text[:context_length]))

    # Prepare the texts for spaCy in the same order
    texts_only = [pt[1].lower() for pt in pages_text]

    # We'll process the entire corpus of page texts in parallel using spaCy pipe
    extracted_rooms = []
    docs = nlp.pipe(texts_only, n_process=n_process, batch_size=batch_size)

    for (page_index, full_text, snippet), doc in zip(pages_text, docs):
        # (A) Dictionary-based Approach using spaCy tokens
        for token in doc:
            if token.text in known_rooms_lower:
                extracted_rooms.append({
                    "room": token.text,           # already lowered
                    "page": page_index,
                    "context_snippet": snippet
                })

        # (B) Regex-based Approach
        matches = compiled_regex.findall(full_text)
        for match_tuple in matches:
            # e.g. ("right", "bedroom", " 1")
            direction = match_tuple[0].strip() if match_tuple[0] else ""
            base_room = match_tuple[1].strip()
            number = match_tuple[2].strip() if match_tuple[2] else ""

            matched_str = " ".join(part for part in [direction, base_room, number] if part)
            print(f"Room Found on page {page_index}: {matched_str}")
            extracted_rooms.append({
                "room": matched_str.lower(),
                "page": page_index,
                "context_snippet": snippet
            })

    # Optionally sort by page if you want them in ascending page order
    extracted_rooms.sort(key=lambda x: x["page"])

    return extracted_rooms

# 3) PROCESS THE PDF PAGE-BY-PAGE
def process_pdf_archive(
    pdf_path: str,
    rooms_list: list,
    compiled_regex: re.Pattern,
    context_length: int = 200
) -> list:
    """
    Opens the PDF file with pdfplumber and searches for room references using:
      - Dictionary-based approach (with spaCy for tokenization/lowercase matching).
      - Regex-based approach (for numeric or directional variations).
    
    :param pdf_path: Path to the PDF file to process.
    :param rooms_list: List of known room strings.
    :param compiled_regex: Pre-compiled regex pattern for matching variations.
    :param context_length: Number of characters from the page text to store as snippet.
    :return: A list of dictionaries, each with keys 'room', 'page', 'context_snippet'.
    """
    # Load spaCy model for basic token-level matching
    nlp = spacy.load("en_core_web_sm")

    # Convert all known rooms to lowercase for direct token matching
    known_rooms_lower = {r.lower() for r in rooms_list}

    extracted_rooms = []

    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")

    with pdfplumber.open(pdf_path) as pdf:
        for page_index, page in enumerate(pdf.pages, start=1):
            raw_text = page.extract_text() or ""
            if not raw_text.strip():
                continue

            # Basic context snippet for reference
            context_snippet = raw_text[:context_length]

            # (A) Dictionary-based Approach with spaCy
            doc = nlp(raw_text.lower())
            for token in doc:
                if token.text in known_rooms_lower:
                    extracted_rooms.append({
                        "room": token.text,
                        "page": page_index,
                        "context_snippet": context_snippet
                    })

            # (B) Regex-based Approach for advanced forms
            matches = compiled_regex.findall(raw_text)
            for match_tuple in matches:
                # match_tuple might look like ("right", "bedroom", " 1") or ("", "mudroom", "")
                # We'll combine them to form a more readable string.
                # Example: direction = "right", base_room = "bedroom", number = " 1"
                direction = match_tuple[0].strip() if match_tuple[0] else ""
                base_room = match_tuple[1].strip()
                number = match_tuple[2].strip() if match_tuple[2] else ""

                # Clean up spacing
                # e.g. "right bedroom 1" or "bedroom 2"
                matched_str = " ".join(part for part in [direction, base_room, number] if part)
                print(f"Potential Room Found on Page {page_index}: {matched_str}")
                extracted_rooms.append({
                    "room": matched_str.lower(),
                    "page": page_index,
                    "context_snippet": context_snippet
                })

    return extracted_rooms

def process_pdf(
    pdf_path: str,
    rooms_list: List[str],
    compiled_regex: re.Pattern,
    context_length: int = 200,
    modifier_range: int = 2  # Number of tokens before and after to check for modifiers
) -> List[Dict[str, str]]:
    """
    Processes a PDF to extract room references with their modifiers.
    
    :param pdf_path: Path to the PDF file.
    :param rooms_list: List of known room names.
    :param compiled_regex: Compiled regex pattern for exact room matches.
    :param context_length: Number of characters to include in the context snippet.
    :param modifier_range: Number of tokens before and after to inspect for modifiers.
    :return: List of dictionaries with 'room', 'page', and 'context_snippet'.
    """
    # Load spaCy model for tokenization
    nlp = spacy.load("en_core_web_sm")
    
    # Define possible modifiers
    modifiers = {"front", "rear", "left", "right", "north", "south", "east", "west",
                 "large", "small", "upper", "lower", "master", "guest", "office",
                 "1", "2", "3", "4", "5"}  # Extend as needed
    
    extracted_rooms = []
    
    # Verify PDF existence
    if not os.path.exists(pdf_path):
        raise FileNotFoundError(f"PDF file not found: {pdf_path}")
    
    with pdfplumber.open(pdf_path) as pdf:
        for page_index, page in enumerate(pdf.pages, start=1):
            raw_text = page.extract_text() or ""
            if not raw_text.strip():
                continue  # Skip empty pages
            
            # Generate context snippet
            context_snippet = raw_text[:context_length].strip()
            
            # Process text with spaCy
            doc = nlp(raw_text)
            
            # Iterate over each token to find matches
            for match in compiled_regex.finditer(raw_text):
                start_char, end_char = match.span()
                matched_text = match.group().strip()
                
                # Find the token index corresponding to the match
                # spaCy's token indices are in character offsets
                start_token = None
                end_token = None
                for token in doc:
                    if token.idx <= start_char < token.idx + len(token):
                        start_token = token.i
                    if token.idx < end_char <= token.idx + len(token):
                        end_token = token.i
                        break
                if start_token is None:
                    continue  # Unable to find token
                
                # Initialize modifiers
                room_modifiers = []
                
                # Check tokens before the matched token for modifiers
                for i in range(start_token - modifier_range, start_token):
                    if i < 0:
                        continue
                    token = doc[i].text.lower()
                    if token in modifiers:
                        room_modifiers.append(token)
                
                # Check tokens after the matched token for modifiers
                for i in range(end_token, end_token + modifier_range):
                    if i >= len(doc):
                        break
                    token = doc[i].text.lower()
                    if token in modifiers:
                        room_modifiers.append(token)
                
                # Combine modifiers with the matched room name
                if room_modifiers:
                    # Order modifiers (optional: define priority if needed)
                    modifiers_sorted = sorted(room_modifiers)
                    full_room_name = ' '.join(modifiers_sorted + [matched_text.lower()])
                else:
                    full_room_name = matched_text.lower()
                
                print(f"Found Room on Page {page_index}: {full_room_name}")
                
                extracted_rooms.append({
                    "room": full_room_name,
                    "page": page_index,
                    "context_snippet": context_snippet
                })
    
    return extracted_rooms

async def add_file_to_vector_store(vector_store_id, file_path, strategy_name="Default"):
    """
    Adds a file to an existing vector store by first uploading the file
    and then associating it with the vector store. Provides selectable chunking strategies.

    Args:
        vector_store_id (str): The ID of the vector store to which the file will be added.
        file_path (str): The path to the file to be added.
        openai_client (object): The OpenAI client object initialized in the main script.
        strategy_name (str, optional): The name of the chunking strategy to use. Default is "Default".
            Options:
                - "Default": Auto chunking (no specific configuration).
                - "Large": Large chunks for structured data (max 1000 tokens, 500 overlap).
                - "Medium": Medium chunks for general use (max 800 tokens, 400 overlap).
                - "Small": Small chunks for fine-grained searches (max 400 tokens, 200 overlap).

    Returns:
        dict: The response from the API as a dictionary.

    Raises:
        Exception: If the file upload or association fails.
    """

    # Hardcoded chunking strategies
    chunking_strategies = {
        "Default": None,  # Auto chunking
        "Large": {
            "type": "static",
            "static": {
                "max_chunk_size_tokens": 1000,
                "chunk_overlap_tokens": 500,
            }
        },
        "Medium": {
            "type": "static",
            "static": {
                "max_chunk_size_tokens": 800,
                "chunk_overlap_tokens": 400,
            }
        },
        "Small": {
            "type": "static",
            "static": {
                "max_chunk_size_tokens": 400,
                "chunk_overlap_tokens": 200,
            }
        },
    }

    # Select the chunking strategy
    chunking_strategy = chunking_strategies.get(strategy_name)
    if chunking_strategy is None and strategy_name != "Default":
        raise ValueError(f"Invalid strategy name: {strategy_name}. Choose from {list(chunking_strategies.keys())}.")

    try:
        # Step 1: Upload the file to OpenAI's storage
        with open(file_path, "rb") as file_stream:
            file_upload_response = await openai_client.files.create(
                file=file_stream,
                purpose='assistants'
            )
        file_id = file_upload_response.id
        print(f"File uploaded successfully. File ID: {file_id}")

        # Step 2: Add the file to the vector store
        payload = {"file_id": file_id}
        if chunking_strategy:
            payload["chunking_strategy"] = chunking_strategy

        vector_store_file_response = await openai_client.beta.vector_stores.files.create(
            vector_store_id=vector_store_id,
            **payload
        )
        
        print("File successfully added to vector store:", vector_store_file_response)
        return vector_store_file_response

    except Exception as e:
        raise Exception(f"Error adding file to vector store: {e}")
    
async def create_vector(vector_store_name: str) -> str:
    """
    Creates a vector store using the provided file content, uploads the content,
    and deletes the temporary file securely.

    Args:
        file_content (str): The content to be uploaded to the vector store.
        vector_store_name (str): The name of the vector store to be created.

    Returns:
        str: The ID of the created vector store.

    Raises:
        Exception: If an error occurs during vector store creation or file deletion.
    """
    try:

        vector_store = await openai_client.beta.vector_stores.create(
            name=vector_store_name
        )
        vector_store_id = vector_store.id

        return vector_store_id

    except Exception as e:
        raise

async def add_message_and_run_agent(message, assistant_id, vector_id = None):  
    thread = await openai_client.beta.threads.create()
    thread_id = thread.id
    if(vector_id):
        await add_vector_store_to_thread(vector_id,thread_id)
        await send_message_to_thread(thread_id, message)
        response = await run_assistant(assistant_id, thread_id)
        return response
    else:
        await send_message_to_thread(thread_id, message)
        response = await run_assistant(assistant_id, thread_id)
        return response

async def add_vector_store_to_thread(vector_store_id, thread_id):
    """
    Adds a vector store to an existing thread and enables file_search.

    Args:
        vector_store_id (str): The ID of the vector store to add.
        thread_id (str): The ID of the thread to modify.
        openai_client (object): The OpenAI client object initialized in the main script.

    Returns:
        dict: The modified thread object.

    Raises:
        Exception: If the API call fails.
    """
    try:
        # Define the tool resources to enable file_search with the vector store
        tool_resources = {
            "file_search": {
                "vector_store_ids": [vector_store_id]
            }
        }

        # Make the API call to modify the thread
        modified_thread = await openai_client.beta.threads.update(
            thread_id=thread_id,
            tool_resources=tool_resources
        )

        print(f"Vector store {vector_store_id} successfully added to thread {thread_id}.")
        return modified_thread

    except Exception as e:
        raise Exception(f"Error adding vector store to thread: {e}")
    
async def send_message_to_thread(thread_id, message):
    """
    Sends a message to the assistant thread.
    """
    await openai_client.beta.threads.messages.create(
        thread_id=thread_id,
        content=message,
        role="user"
    )

async def run_assistant(assistant_id, thread_id, max_tokens=8000):
    """
    Executes an assistant thread run and retrieves the response, with timeout and retry logic.
    
    Args:
        assistant_id (str): The ID of the assistant.
        thread_id (str): The ID of the thread.
        max_tokens (int): The maximum number of tokens for the run.
    
    Returns:
        str: The assistant's response.
    """
    attempt = 0  # Tracks the current retry attempt

    while attempt < MAX_RETRIES:
        attempt += 1
        #logging.info(f"Starting run attempt {attempt} for assistant_id={assistant_id}, thread_id={thread_id}")

        try:
            # Step 1: Create a new run
            run = await openai_client.beta.threads.runs.create(
                thread_id=thread_id,
                assistant_id=assistant_id,
                max_completion_tokens=max_tokens
            )
            run_id = run.id
            #logging.info(f"Run created: run_id={run_id}, initial_status={run.status}")

            # Step 2: Monitor run status with timeout
            start_time = asyncio.get_event_loop().time()
            while run.status not in {"completed", "failed", "cancelled", "incomplete"}:
                elapsed_time = asyncio.get_event_loop().time() - start_time
                if elapsed_time > RUN_TIMEOUT_SECONDS:
                    #logging.warning(f"Run {run_id} timed out after {elapsed_time} seconds. Retrying...")
                    await cancel_run(thread_id, run_id)
                    raise TimeoutError(f"Run {run_id} exceeded timeout of {RUN_TIMEOUT_SECONDS} seconds.")
                #logging.debug(f"Run {run_id} current status: {run.status}")
                await asyncio.sleep(1)

                run = await openai_client.beta.threads.runs.retrieve(
                    run_id=run_id,
                    thread_id=thread_id
                )
                #logging.debug(f"Run status updated: run_id={run_id}, current_status={run.status}")

            # Step 3: Handle terminal states
            if run.status == "completed":
                #logging.info(f"Run {run_id} completed successfully.")
                return await fetch_run_response(thread_id, run_id)
            elif run.status == "failed":
                logging.warning(f"Run {run_id} failed. Retrying... Attempt {attempt} of {MAX_RETRIES}.")
            elif run.status == "incomplete":
                logging.warning(f"Run {run_id} incomplete. Retrying... Attempt {attempt} of {MAX_RETRIES}.")
            elif run.status == "cancelled":
                logging.warning(f"Run {run_id} cancelled. Retrying... Attempt {attempt} of {MAX_RETRIES}.")

        except TimeoutError as te:
            logging.error(f"Timeout error: {te}")
        except Exception as e:
            logging.error(f"Error during run attempt {attempt}: {e}", exc_info=True)

        # If the loop hasn't returned or raised, retry.
        #logging.info(f"Retrying run for assistant_id={assistant_id}, thread_id={thread_id}. Attempt {attempt}/{MAX_RETRIES}.")

    # If retries are exhausted, raise an exception
    raise Exception(f"Failed to complete run after {MAX_RETRIES} attempts.")

async def cancel_run(thread_id, run_id):
    """
    Cancels a run by its thread and run ID.
    
    Args:
        thread_id (str): The ID of the thread.
        run_id (str): The ID of the run to cancel.
    """
    try:
        run = await openai_client.beta.threads.runs.cancel(
            thread_id=thread_id,
            run_id=run_id
        )
        logging.info(f"Run {run_id} cancellation initiated: status={run.status}")
    except Exception as e:
        logging.error(f"Error cancelling run {run_id}: {e}", exc_info=True)

async def query_deepseek_v3(prompt, model="deepseek-chat", temperature=0.7, max_tokens=40000, max_retries=3, timeout=200):
    """
    Send a query to the DeepSeek V3 model through OpenRouter and return the response.
    Automatically retries if the request times out or fails.

    Parameters:
    - prompt (str): The input query for the model.
    - model (str): The DeepSeek model to use (default: "deepseek/deepseek-chat").
    - temperature (float): Sampling temperature (0-2).
    - max_tokens (int): Maximum tokens for the response.
    - max_retries (int): Maximum number of retries in case of failure or timeout.
    - timeout (int): Timeout duration in seconds for each request.

    Returns:
    - str: Response content from the model, or None if all retries fail.
    """
    for attempt in range(max_retries):
        try:
            # Set timeout for the request
            response = await asyncio.wait_for(
                asyncio.to_thread(client.chat.completions.create,  # Run the blocking request in a thread
                                  model=model,
                                  messages=[{"role": "user", "content": prompt}],
                                  temperature=temperature,
                                  max_tokens=max_tokens),
                timeout=timeout
            )
            # Extract and return the message content
            #print(response)
            return response.choices[0].message.content

        except asyncio.TimeoutError:
            if DEBUGGING:
                    print(f"[yellow]Timeout: Attempt {attempt + 1}/{max_retries} exceeded {timeout} seconds. Retrying...[/yellow]")
        except Exception as e:
            #logging.error(f"Error querying DeepSeek V3 on attempt {attempt + 1}/{max_retries}: {str(e)}")
            print("".join(traceback.format_exception(type(e), e, e.__traceback__)))
        except Exception as e:
            if DEBUGGING:
                    print(f"[red]Error querying DeepSeek V3 on attempt {attempt + 1}/{max_retries}: {e}\n{response}[/red]")
        
        # Wait a bit before retrying
        await asyncio.sleep(2)

    # If all retries fail, return None
    if DEBUGGING:
        print(f"[red]All {max_retries} attempts failed. Returning None.[/red]")
    return None


async def get_o1_response(prompt, agent_instructions):
    completion = await openai_client.chat.completions.create(
    model="o1-mini",
    messages=[
        {"role": "assistant", "content": agent_instructions},
        {"role": "user", "content": prompt}
    ]
    )
    #print(completion)
    message = completion.choices[0].message.content
    return message

async def fetch_run_response(thread_id, run_id):
    """
    Fetches the response of a completed run and ensures it returns a simple string.
    
    Args:
        thread_id (str): The ID of the thread.
        run_id (str): The ID of the run.
    
    Returns:
        str: The content of the run's response message as a simple string.
    """
    try:
        messages = await openai_client.beta.threads.messages.list(
            thread_id=thread_id,
            run_id=run_id,
            limit=1
        )
        
        """ if messages and messages.data:
            # Extract the content from the first message
            #logging.info(f"OpenAI Message Response: {str(messages)}")
            message_content = messages.data[0].content
            #logging.info(f"Initial Output from OpenAI: {message_content}")

            # Parse message content to a simple string
            #result = parse_message_content_to_string(message_content)
            message_content = clean_string(message_content)
            result = (str(message_content).replace("\\n", "\n")  # Replace escaped newlines with actual line breaks
              .replace("[TextContentBlock(text=Text(annotations=[], value='", "")
              .replace("TextContentBlock(text=Text(annotations=[], value='", "")
              .replace("', type='text')]", "")
              .replace("', type='text')", "")
              .replace('[TextContentBlock(text=Text(annotations=[], value="', "")
              .replace('TextContentBlock(text=Text(annotations=[], value="', "")
              .replace('", type="text")]', "")
              .replace('", type="text")', ""))
            #logging.info(f"Processed Output from OpenAI: {result}")

            return result
        else:
            raise ValueError("No valid response content found in the run messages.") """
        
        if messages and messages.data:
                message_content = messages.data[0].content
                #print("Message Content Debug:", message_content)
                
                # Extract text from message content
                if message_content and isinstance(message_content, list):
                    for item in message_content:
                        if hasattr(item, "type") and item.type == "text" and hasattr(item, "text"):
                            return item.text.value.strip()
        
    except Exception as e:
        #logging.error(f"Error fetching response for run {run_id}: {e}", exc_info=True)
        raise

async def list_vector_store_files(vector_store_id):
    """
    Lists all files associated with a given vector store.
    
    Args:
        vector_store_id (str): The ID of the vector store.
        
    Returns:
        list: A list of file metadata dictionaries.
    """
    try:
        response = await openai_client.beta.vector_stores.files.list(vector_store_id=vector_store_id)
        return response["data"] if "data" in response else []
    except Exception as e:
        print(f"Error listing files for vector store {vector_store_id}: {e}")
        return []


def get_file_path():
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    file_path = filedialog.askopenfilename(title="Select a file")
    return file_path
    
def send_email(subject, body, recipient):
    """
    Sends an email using Gmail SMTP server with the credentials and recipient specified in .env and hardcoded variables.
    """
    smtp_username = "<EMAIL>"  # Sender email
    smtp_password = "ssnz upip wvay lldj"  # Sender password (or app password for Gmail)
    recipient_email = recipient
    smtp_server = "smtp.gmail.com"
    smtp_port = 587

    if not smtp_username or not smtp_password:
        raise ValueError("SMTP_USERNAME or SMTP_PASSWORD is missing in the .env file.")

    msg = MIMEText(body)
    msg['Subject'] = subject
    msg['From'] = smtp_username
    msg['To'] = recipient_email

    try:
        with SMTP(smtp_server, smtp_port) as server:
            server.starttls()  # Encrypt the connection
            server.login(smtp_username, smtp_password)
            server.send_message(msg)
            print(f"Email sent successfully to {recipient_email}")
    except Exception as e:
        print(f"Failed to send email: {e}")

def create_temp_pdf_subset(original_pdf_path, page_numbers):
    """
    Creates a temporary PDF containing only the specified page numbers from 'original_pdf_path'.
    Automatically deletes the temp PDF upon completion of this function.

    :param original_pdf_path: Path to the original PDF file.
    :param page_numbers: List of 1-based page indices to extract (e.g., [12, 16, 20]).
    :return: (None) The temp file is deleted when this function completes.
    """
    with open(original_pdf_path, 'rb') as infile:
        reader = PyPDF2.PdfReader(infile)
        writer = PyPDF2.PdfWriter()

        for pnum in page_numbers:
            # Convert 1-based index to 0-based for PyPDF2
            writer.add_page(reader.pages[pnum - 1])

        # Create a temporary file that is automatically deleted on close
        with tempfile.NamedTemporaryFile(suffix=".pdf", delete=True) as tmp_pdf:
            writer.write(tmp_pdf)   # Write the extracted subset to the temp file
            tmp_pdf.seek(0)        # If you need to read or pass it somewhere, do so here

            # Example: read the temp PDF data into memory
            pdf_subset_bytes = tmp_pdf.read()
            # (Pass `pdf_subset_bytes` to another function, or upload it somewhere, etc.)

            # The file is still there on disk until we exit the 'with' block.
            # After the 'with' block closes, tmp_pdf is automatically deleted.

    # Nothing to return if your only goal is to produce a temp PDF then delete it automatically.
    # If you'd like to return the bytes or do additional processing, handle it before exiting the block.

from collections import defaultdict
from typing import List, Dict

def aggregate_rooms(rooms_found: List[Dict[str, str]]) -> Dict[str, List[Dict[str, str]]]:
    """
    Aggregates room occurrences into a dictionary with room names as keys.

    :param rooms_found: List of dictionaries with keys 'room', 'page', and 'context_snippet'.
    :return: Dictionary with room names as keys and lists of occurrences as values.
    """
    aggregated = defaultdict(list)
    
    for entry in rooms_found:
        room_name = entry['room']
        page = entry['page']
        context = entry['context_snippet']
        
        # Append occurrence to the corresponding room
        aggregated[room_name].append({
            "page": page,
            "context_snippet": context
        })
    
    return dict(aggregated)
def extract_and_combine_json_objects(text, combine_as_dict=True):
    """
    Extracts JSON objects from a string and combines them into a single JSON structure.

    Args:
        text (str): The string containing JSON objects.
        combine_as_dict (bool): Whether to combine JSON objects into a dictionary (default) or a list.

    Returns:
        dict, list, or single JSON object: Combined JSON structure or single JSON object if only one is found.
    """
    combined_json = {} if combine_as_dict else []
    try:
        # Regular expression to match JSON-like objects
        json_pattern = re.compile(r"\{.*?\}", re.DOTALL)

        # Find all JSON-like objects in the string
        matches = json_pattern.findall(text)

        # If only one JSON object is found, return it directly
        if len(matches) == 1:
            try:
                return json.loads(matches[0])
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON format in the single found object.")

        # If multiple JSON objects are found, combine them
        for i, match in enumerate(matches):
            try:
                # Parse each JSON object
                json_object = json.loads(match)
                if combine_as_dict:
                    # Combine as a dictionary with unique keys
                    combined_json[f"object_{i + 1}"] = json_object
                else:
                    # Combine as a list
                    combined_json.append(json_object)
            except json.JSONDecodeError:
                # Skip invalid JSON fragments
                continue

    except Exception as e:
        if DEBUGGING:
                    print(f"An error occurred: {e}")

    return combined_json

def extract_json_line_items(input_string):
    """
    Extracts a JSON object from the input string and returns it as a Python dictionary.

    Args:
        input_string (str): The string containing the JSON object.

    Returns:
        dict: The extracted JSON object.

    Raises:
        ValueError: If no JSON object is found or if the JSON is malformed.
    """
    
    # Find the first '{'
    start = input_string.find('{')
    if start == -1:
        raise ValueError("No JSON object found in the input string.")

    brace_count = 0
    end = start
    in_string = False
    escape = False

    for i, char in enumerate(input_string[start:], start=start):
        if char == '"' and not escape:
            in_string = not in_string
        if not in_string:
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    end = i + 1
                    break
        if char == '\\' and not escape:
            escape = True
        else:
            escape = False
    else:
        raise ValueError("No matching closing brace found for JSON object.")

    json_str = input_string[start:end]

    try:
        json_obj = json.loads(json_str)
    except json.JSONDecodeError as e:
        raise ValueError(f"Error parsing JSON: {e}")

    return json_obj

async def extract_line_items(room_bundle: Dict[str, str], vector_id) -> Dict[str, str]:
    """
    Extracts line items and dimensions for a single room bundle using an AI agent.
    Args:
    room_bundle (Dict[str, Any]): A room bundle containing room name, page number, and context.
    Returns:
    Dict[str, Any]: Dictionary containing room name, page number, and extracted line items.
    """
    prompt = """Your job is to comb through the attached file and extract ALL of the line items related to the below room. If the room is invalid, simply return a null object. There is an OCR process prior to you which will often collect rooms which aren't rooms simply because the words exist in the document. If there are elements like numbers or other improper words around the room name, remove them. The OCR is imperfect.

    Once you identify the exact right room, using the Page Number guidance (which isn't exact), you must extract and provide ONLY the room line items in PRECISE JSON. Provide no text outside the JSON or the process will fail.

    Under NO circumstances should numbers ever contain commas, as this will break it. The only change allowed to the extracted values are changes which allow the data to be parsed correctly as JSON fields.

    The structure of the JSON must be (With EXACT naming conventions and EXACT extraction):
    {
        "room_name": {  // The name of the primary room (e.g., "Living Room", "Kitchen"). Remove any additions to it which don't fit and seem like erroneous additions from the OCR.
            "subroom": {
            "subroom_name": {  // The name of the subroom, typically the same as the base room (e.g., "Living Room")
                "square_footage": 0,  // Numeric value representing the total square footage of the subroom
                "line_items": [
                {
                    "DESCRIPTION": "Enter item/service description here",  // A brief description of the item or service
                    "QTY": 0,  // Numeric value indicating the quantity of the item/service
                    "RESET": 0.00,  // Cost related to resetting the item/service
                    "REMOVE": 0.00,  // Cost for removing the item/service
                    "REPLACE": 0.00,  // Cost for replacing the item/service
                    "TAX": 0.00,  // Tax applied to the item/service
                    "TOTAL": 0.00  // Total cost, including tax and other applicable charges
                }
                ]
            }
            }
        }
    }

    The room for you to analyze is: 
        """
    # Example implementation using an AI agent (e.g., GPT, Claude, etc.)
    # Replace with your actual AI agent logic
    room_name = room_bundle["room_name"]
    pages = room_bundle["page_number"]
    #context = room_bundle["context"]
    # Simulate AI agent extraction
    extracted_items = await add_message_and_run_agent(f"{prompt} {room_name} on pages {pages}", data_extractor_id, vector_id)
    print(f"extracted_items: {extracted_items}")
    line_items = extract_json_line_items(extracted_items)
    print(f"line_items: {line_items}")
    return line_items

async def process_rooms_concurrently(
    aggregated_rooms: Dict[str, List[Dict[str, str]]],
    csv_filepath: str,
    vector_id: str
) -> None:
    """
    Processes each room in aggregated_rooms by calling extract_line_items concurrently in batches of 5.
    Writes each extracted line item to a CSV file. Creates the file with headers if it doesn't exist.
    
    :param aggregated_rooms: Dictionary with room names as keys and lists of occurrences as values.
    :param csv_filepath: Path to the CSV file to write results.
    :param vector_id: Identifier for the vector or process (used in extract_line_items).
    :param data_extractor_id: Identifier for the data extractor agent (used in extract_line_items).
    """
    # Define CSV headers based on the JSON template
    headers = [
        'room_name',
        'subroom_name',
        'square_footage',
        'DESCRIPTION',
        'QTY',
        'RESET',
        'REMOVE',
        'REPLACE',
        'TAX',
        'TOTAL'
    ]

    # Check if the CSV file exists. If not, create it and write headers.
    if not os.path.isfile(csv_filepath):
        with open(csv_filepath, mode='w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()

    # Create an asyncio.Lock to ensure that only one coroutine writes to the file at a time
    csv_lock = asyncio.Lock()

    async def process_and_write(room_name: str, occurrences: List[Dict[str, str]]):
        """
        Processes a single room by calling extract_line_items and writes the extracted data to CSV.
        
        :param room_name: Name of the room.
        :param occurrences: List of occurrences with page numbers and context snippets.
        """
        # Aggregate unique pages and concatenate context snippets
        pages = sorted({entry['page'] for entry in occurrences})
        context_snippets = "\n".join({entry['context_snippet'] for entry in occurrences})

        # Prepare the room_bundle for extraction
        room_bundle = {
            "room_name": room_name,
            "page_number": pages,
            "context_snippet": context_snippets
        }

        # Call the extract_line_items function (assumed to be defined elsewhere)
        extracted_json_raw = await extract_line_items(room_bundle, vector_id)
        print(f"extracted_json_raw: {extracted_json_raw}")
        #extracted_json = extracted_json_raw.replace("```json\n", "").replace("\n```", "").strip()
        extracted_json = extracted_json_raw
        print(f"extracted_json: {extracted_json}")

        # Navigate through the JSON structure to extract line items
        # Example JSON Structure:
        # {
        #     "Living Room": {
        #         "subroom": {
        #             "Living Room": {
        #                 "square_footage": 300,
        #                 "line_items": [
        #                     {
        #                         "DESCRIPTION": "Sofa",
        #                         "QTY": 1,
        #                         "RESET": 100.00,
        #                         "REMOVE": 50.00,
        #                         "REPLACE": 200.00,
        #                         "TAX": 15.00,
        #                         "TOTAL": 365.00
        #                     },
        #                     ...
        #                 ]
        #             }
        #         }
        #     }
        # }

        # Access the subroom data directly
        subroom_data = extracted_json.get("subroom", {})

        # Extract necessary fields
        subroom_name = subroom_data.get("subroom_name", "")
        square_footage = subroom_data.get("square_footage", 0)
        line_items = subroom_data.get("line_items", [])

        for item in line_items:
            # Prepare the row data
            row = {
                'room_name': room_name,
                'subroom_name': subroom_name,
                'square_footage': square_footage,
                'DESCRIPTION': item.get("DESCRIPTION", ""),
                'QTY': item.get("QTY", 0),
                'RESET': item.get("RESET", 0.00),
                'REMOVE': item.get("REMOVE", 0.00),
                'REPLACE': item.get("REPLACE", 0.00),
                'TAX': item.get("TAX", 0.00),
                'TOTAL': item.get("TOTAL", 0.00)
            }

            # Write the row to CSV with thread safety
            async with csv_lock:
                with open(csv_filepath, mode='a', newline='', encoding='utf-8') as csvfile:
                    writer = csv.DictWriter(csvfile, fieldnames=headers)
                    writer.writerow(row)


    # Split the rooms into batches of 5
    rooms = list(aggregated_rooms.items())
    batch_size = 5
    batches = [rooms[i:i + batch_size] for i in range(0, len(rooms), batch_size)]

    # Process each batch sequentially
    for batch in batches:
        tasks = []
        for room_name, occurrences in batch:
            task = asyncio.create_task(process_and_write(room_name, occurrences))
            tasks.append(task)
        # Await the completion of the current batch
        await asyncio.gather(*tasks)


async def main():
    room_prompt = """
        Objective:
        Extract the exact names of all primary rooms explicitly listed in the provided claim document, along with their respective square footage. Do not infer or guess room names or sizes; only return what is explicitly stated in the document. Ensure accuracy and completeness while preserving the document's formatting conventions.

        Instructions:
        1. Identify all occurrences of primary room names exactly as written in the document. Do NOT identify sub-elements such as "closet," "bathroom," "island," or any other subrooms associated with the main room. ONLY extract the primary rooms and their corresponding square footage.
        2. If a room appears multiple times with the same name (e.g., multiple "Bedroom" entries), assign a unique identifier to each occurrence by appending a sequential number (e.g., "Bedroom 1", "Bedroom 2") while preserving the document's naming.
        3. Extract room square footage if explicitly provided. If only dimensions (length x width) are provided, calculate the square footage and include a note indicating that the value was derived from the provided dimensions.
        4. Ensure that extracted values are consistent with the document formatting, maintaining original spelling, punctuation, and capitalization.
        5. Exclude any descriptive text, irrelevant sections, or inferred entries. Ignore any line items or notes. Extract ONLY the primary rooms and their square footage from the claim.
        6. Consider all sections, including room-specific headings, summary lists, and line-itemized areas, but ensure you ONLY extract the EXACT primary rooms without sub-elements.

        Output Format:
        Provide the extracted primary room list in JSON format using the following structure:

        {
        "documentTitle": "<title_of_document>",
        "rooms": [
            {
            "roomName": "<exact_room_name>",
            "squareFootage": <room_square_footage>,
            "calculationNote": "<optional_calculation_note>"
            },
            {
            "roomName": "<exact_room_name> 1",
            "squareFootage": <room_square_footage>,
            "calculationNote": "<optional_calculation_note>"
            }
        ],
        "totalRooms": <number_of_primary_rooms_extracted>
        }

        Example Output:

        {
        "documentTitle": "Claim_Name_Test123",
        "rooms": [
            {
            "roomName": "Living Room",
            "squareFootage": 300,
            "calculationNote": "Value derived from dimensions 15x20 ft"
            },
            {
            "roomName": "Master Bedroom",
            "squareFootage": 168,
            "calculationNote": "Value derived from dimensions 12' x 14'"
            },
            {
            "roomName": "Bedroom 1",
            "squareFootage": 120,
            "calculationNote": "Value derived from dimensions 10x12 ft"
            },
            {
            "roomName": "Bedroom 2",
            "squareFootage": 110,
            "calculationNote": "Value derived from dimensions 10x11 ft"
            },
            {
            "roomName": "Kitchen",
            "squareFootage": 180,
            "calculationNote": "Value derived from dimensions 18' x 10'"
            }
        ],
        "totalRooms": 5
        }

        Important Notes:
        - Ensure all extracted data is directly sourced from the document without modification.
        - If dimensions (e.g., "12x14 ft") are found instead of square footage, calculate the total area and add a calculation note in the output. Additionally, if a blueprint or design is provided try to use that as well to calculate.
        - If square footage is explicitly stated, use it without modification and leave 'calculationNote' empty.
        - If a section header contains multiple rooms (e.g., "Bedrooms 1 & 2"), treat them separately as "Bedroom 1" and "Bedroom 2" only if explicitly listed.
        - Do NOT extract subrooms such as closets, bathrooms, or other subdivisions of a main room.
        - Assign unique identifiers to rooms with the same name, preserving original naming conventions.
        - If no primary rooms are found, return an empty 'rooms' array with 'totalRooms' set to 0.
        """
    ocr_cleanup_prompt = """
        Objective:
        Parse the provided OCR extraction string to identify and return likely distinct room names in a structured JSON format. Each identified room should include its name, the page number(s) it appears on, and a brief explanation categorizing the confidence of it being an actual room. The goal is to refine the list by removing false matches and consolidating valid room entries.

        Instructions:
        1. Extract room names exactly as they appear, based on commonly expected terms such as "kitchen," "bathroom," "garage," etc.
        2. Differentiate instances of rooms with the same name that appear in different locations or contexts (e.g., "Stairs" on page 2 vs. page 10 might indicate distinct areas).
        3. Extract and consolidate page numbers where each room appears, grouping related instances but keeping distinct rooms separate based on context.
        4. Provide a brief explanation that categorizes each room entry as:
        - **"Likely a Room"**: Strong indicators that this is a valid room based on surrounding context.
        - **"Needs Confirmation"**: Potential room but requires further review to confirm its validity.
        - **"Unlikely a Room"**: Context suggests it's not a room (e.g., part of a company name or unrelated term).
        5. Discard entries that are confidently false positives (e.g., text repetitions, supplier names, non-room-related labels).
        6. If no likely rooms are found, return an empty array.

        Output Format:
        Provide the extracted room list in JSON format using the following structure:

        {
        "rooms": [
            {
            "roomName": "<exact_room_name>",
            "pages": [<page_number>, <page_number>], - NOTE THIS MUST BE EXACT
            "briefExplanation": "<category: Likely a Room, Needs Confirmation, Unlikely a Room> - <3 sentence explanation of what the later agents should check or the rationale behind your explanation in a single line>"
            }
        ],
        "totalRooms": <number_of_likely_rooms_extracted which should sum exactly to the above array>
        }

        Important Notes:
        - Extract exact spelling and capitalization from the OCR text without modification but correct the room names if the context determines you should (we are using a regex matching function and it might strip elements from the extracted name)
        - Differentiate rooms of the same name by assessing context and page distribution.
        - Ignore false positive matches from company names, repetitive headers, and unrelated content.
        - If no plausible rooms are found, return an empty 'rooms' array and set 'totalRooms' to 0.
        - Err on the side of including POTENTIAL rooms which could need more context to be sure. The goal is to avoid missing any as your team will review and confirm this list next.
        - Just to emphasize, it is better for the list to include elements which AREN'T rooms than for a single real room to be missed.
        
        Content to evaluate (the format is <Room Name> - <Page Number> - <100 tokens of surrounding context>) is below. It is extracted by an automated OCR and regex matching and you are responsible for cleaning it and identifying the likely rooms from it to be fed to the team later. Please be exact and comprehensive.

        Content to review: 
        """
    ocr_cleanup_agent = "You are a consummate expert in parsing insurance claims documents specialized in parsing OCR-extracted room data, identifying and consolidating likely room entries while filtering out false positives, categorizing confidence levels, and structuring results for downstream processing."
    file_path = get_file_path()
    if(True):
        # Adjust paths as needed
        JSON_PATH = "room_names.json"
        PDF_PATH = file_path

        # 1) Load the room list from JSON
        rooms = load_rooms_from_json(JSON_PATH)
        
        # 2) Build the regex pattern
        room_pattern = compile_room_regex(rooms)

        # 3) Process the PDF
        results = process_pdf(PDF_PATH, rooms, room_pattern, context_length=150)
        results_string = ''
        # 4) Print or store the results
        #for entry in results:
        #    # print(f"Page {entry['page']} -> {entry['room']} -> {entry['context_snippet'].replace("\n", "\\n").replace("\r", "\\r")}")
        #    results_string = f"{results_string}\n{entry['room']} - {entry['page']} - {entry['context_snippet'].replace("\n", "\\n").replace("\r", "\\r")}"
        #    print(results_string)
            # If you need more context, you can access entry["context_snippet"]
            # print(results_string)

        # Aggregate the room data
        aggregated_rooms = aggregate_rooms(results)

        #print(aggregated_rooms)

        vector_id = await create_vector("Atlas Demo")
        await add_file_to_vector_store(vector_id, file_path,"Large")

        await process_rooms_concurrently(aggregated_rooms, "Test Output.csv", vector_id)

        #likely_rooms = await add_message_and_run_agent(f"{ocr_cleanup_prompt}\n{results_string}", data_extractor_id)
        #likely_rooms = await get_o1_response(f"{ocr_cleanup_prompt}\n{results_string}", ocr_cleanup_agent)
        #likely_rooms = await query_deepseek_v3(f"Agent Instructions: {ocr_cleanup_agent}\n\nTask Instructions: {ocr_cleanup_prompt}\n\n{results_string}","deepseek-chat",0.2,8192)
        #print(likely_rooms)
            #reduped_room_json = await add_message_and_run_agent(dedupe_prompt, data_extractor_id)
    if(False):
        vector_store_id = await create_vector("Atlas Room Extraction") #Replace with claim name or something
        await add_file_to_vector_store(vector_store_id, file_path, "Large")
        room_names = await add_message_and_run_agent(room_prompt, data_extractor_id, vector_store_id)
        print(room_names)
        correction_prompt = """
            Please review your last result and identify any issues, address them, then render in the identified formatting. The documents can be large so ensure you fully sample it. Provide the corrected JSON and ONLY the corrected JSON. Ensure you provide ALL identified rooms and their associated square footage including the ones previously identified. The goal is for this output to have a comprehensive listing.
            
            1. Identify all occurrences of primary room names exactly as written in the document. Do NOT identify sub-elements such as "closet," "bathroom," "island," or any other subrooms associated with the main room. ONLY extract the primary rooms and their corresponding square footage.
            2. If a room appears multiple times with the same name (e.g., multiple "Bedroom" entries), assign a unique identifier to each occurrence by appending a sequential number (e.g., "Bedroom 1", "Bedroom 2") while preserving the document's naming.
            3. Extract room square footage if explicitly provided. If only dimensions (length x width) are provided, calculate the square footage and include a note indicating that the value was derived from the provided dimensions.
            4. Ensure that extracted values are consistent with the document formatting, maintaining original spelling, punctuation, and capitalization.
            5. Exclude any descriptive text, irrelevant sections, or inferred entries. Ignore any line items or notes. Extract ONLY the primary rooms and their square footage from the claim.
            6. Consider all sections, including room-specific headings, summary lists, and line-itemized areas, but ensure you ONLY extract the EXACT primary rooms without sub-elements.

            Output Format:
            Provide the extracted primary room list in JSON format using the following structure and including NO other content:

            {
            "documentTitle": "<title_of_document>",
            "rooms": [
                {
                "roomName": "<exact_room_name>",
                "squareFootage": <room_square_footage>,
                "calculationNote": "<optional_calculation_note>"
                },
                {
                "roomName": "<exact_room_name> 1",
                "squareFootage": <room_square_footage>,
                "calculationNote": "<optional_calculation_note>"
                }
            ],
            "totalRooms": <number_of_primary_rooms_extracted>
            }

            Important Notes:
            - Ensure all extracted data is directly sourced from the document without modification.
            - If dimensions (e.g., "12x14 ft") are found instead of square footage, calculate the total area and add a calculation note in the output. Additionally, if a blueprint or design is provided try to use that as well to calculate.
            - If square footage is explicitly stated, use it without modification and leave 'calculationNote' empty.
            - If a section header contains multiple rooms (e.g., "Bedrooms 1 & 2"), treat them separately as "Bedroom 1" and "Bedroom 2" only if explicitly listed.
            - Do NOT extract subrooms such as closets, bathrooms, or other subdivisions of a main room.
            - Assign unique identifiers to rooms with the same name, preserving original naming conventions.
            - If no primary rooms are found, return an empty 'rooms' array with 'totalRooms' set to 0.
            """
        room_names = await add_message_and_run_agent(correction_prompt, data_extractor_id, vector_store_id)
        print(room_names)

if __name__ == "__main__":
    asyncio.run(main())