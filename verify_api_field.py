#!/usr/bin/env python3
"""
Verify that the API key field is in the HTML template
"""

def check_html_file():
    """Check if API key field exists in the HTML file"""
    try:
        with open('templates/demo.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for API key related elements
        checks = [
            ('API Key Label', 'OpenAI API Key:' in content),
            ('API Key Input', 'name="api_key"' in content),
            ('API Key ID', 'id="apiKey"' in content),
            ('API Key Section', 'api-key-section' in content),
            ('Toggle Button', 'toggleApiKeyVisibility' in content),
            ('Password Input', 'type="password"' in content),
            ('Placeholder', 'sk-proj-' in content)
        ]
        
        print("🔍 Checking HTML template for API key field...")
        print("=" * 50)
        
        all_good = True
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"{status} {check_name}: {'Found' if result else 'Missing'}")
            if not result:
                all_good = False
        
        print("=" * 50)
        if all_good:
            print("🎉 All API key elements found in HTML template!")
        else:
            print("⚠️  Some API key elements are missing!")
        
        # Show a snippet around the API key section
        if 'api-key-section' in content:
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'api-key-section' in line:
                    print(f"\n📄 HTML snippet around line {i+1}:")
                    start = max(0, i-3)
                    end = min(len(lines), i+10)
                    for j in range(start, end):
                        marker = ">>> " if j == i else "    "
                        print(f"{marker}{j+1:3d}: {lines[j]}")
                    break
        
        return all_good
        
    except FileNotFoundError:
        print("❌ templates/demo.html file not found!")
        return False
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def check_css_file():
    """Check if API key styles exist in CSS file"""
    try:
        with open('static/demo.css', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('API Key Section Style', '.api-key-section' in content),
            ('API Key Input Style', '.api-key-section input' in content),
            ('Toggle Button Style', '.btn-toggle-key' in content),
            ('API Key Help Style', '.api-key-help' in content)
        ]
        
        print("\n🎨 Checking CSS file for API key styles...")
        print("=" * 50)
        
        all_good = True
        for check_name, result in checks:
            status = "✅" if result else "❌"
            print(f"{status} {check_name}: {'Found' if result else 'Missing'}")
            if not result:
                all_good = False
        
        return all_good
        
    except FileNotFoundError:
        print("❌ static/demo.css file not found!")
        return False
    except Exception as e:
        print(f"❌ Error reading CSS file: {e}")
        return False

if __name__ == "__main__":
    print("🔍 ATHENA DEMO API KEY FIELD VERIFICATION")
    print("=" * 60)
    
    html_ok = check_html_file()
    css_ok = check_css_file()
    
    print("\n" + "=" * 60)
    if html_ok and css_ok:
        print("🎉 VERIFICATION PASSED: API key field should be visible!")
        print("💡 If you don't see it in the browser, try:")
        print("   1. Hard refresh (Ctrl+F5 or Cmd+Shift+R)")
        print("   2. Clear browser cache")
        print("   3. Try a different browser")
        print("   4. Check browser console for JavaScript errors")
    else:
        print("❌ VERIFICATION FAILED: API key field has issues!")
        print("💡 The field may not display correctly in the browser.")
    
    print(f"\n🌐 Demo URL: http://localhost:5000")
    print("🚀 Start demo with: python demo_interface.py")
