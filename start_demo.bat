@echo off
echo ========================================
echo    ATHENA DEMO INTERFACE LAUNCHER
echo ========================================
echo.
echo Starting Athena Demo...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Install Flask if not present
python -c "import flask" >nul 2>&1
if errorlevel 1 (
    echo Installing Flask for demo interface...
    pip install Flask==2.3.3 Werkzeug==2.3.7
)

REM Start the demo
echo.
echo Opening Athena Demo Interface...
echo Browser will open automatically at http://localhost:5000
echo.
echo Press Ctrl+C to stop the demo
echo.

python start_demo.py

pause
