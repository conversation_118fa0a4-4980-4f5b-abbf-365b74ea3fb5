import PyPDF2
import pdfplumber
import asyncio
import os
import io
import json
import tkinter as tk
from tkinter import filedialog
import pdf2image
import pandas as pd
import tempfile
import base64
from PIL import Image
from pdf2image import convert_from_bytes
import pytesseract

def extract_tables_from_pdf_page(pages_data):
    extracted_tables = []
    for page_num, page_data in enumerate(pages_data, start=1):
        # 1. Try PDFPlumber
        with pdfplumber.open(io.BytesIO(page_data)) as pdf:
            page = pdf.pages[0]
            tables = page.extract_tables()
            if tables:
                for table in tables:
                    df = pd.DataFrame(table[1:], columns=table[0])
                    extracted_tables.append(df)
                    print(df)
                continue
        

def extract_text_from_pdf_page(page_bytes, dpi=300, language='eng', poppler_path=r'C:\poppler-23.05.0\Library\bin'):
    """
    Extracts text from a single-page PDF using OCR.

    :param page_bytes: Byte string of the single-page PDF.
    :param dpi: Resolution for the PDF to image conversion. Higher DPI can improve OCR accuracy.
    :param language: Language(s) to use for OCR. Default is English ('eng').
                     For multiple languages, use a '+' separator, e.g., 'eng+spa'.
    :return: Extracted text as a single long string.
    """
    try:
        # Convert PDF bytes to images
        images = convert_from_bytes(page_bytes, dpi=dpi, poppler_path=poppler_path)
        
        if not images:
            raise ValueError("No images were extracted from the PDF page.")

        # Assuming single-page PDF, take the first image
        image = images[0]

        # Optional: Preprocess the image for better OCR accuracy
        # For example, convert to grayscale and apply thresholding
        # image = image.convert('L')
        # image = image.point(lambda x: 0 if x < 128 else 255, '1')

        # Perform OCR using pytesseract
        extracted_text = pytesseract.image_to_string(image, lang=language)

        # Clean up the extracted text (optional)
        extracted_text = extracted_text.strip()

        return extracted_text

    except Exception as e:
        print(f"Error during OCR extraction: {e}")
        return ""

def pdf_page_to_base64(page_bytes):
    """
    Converts a single-page PDF (in bytes) into an image and returns it as a base64-encoded string.

    :param page_bytes: Byte string of a single-page PDF.
    :return: Base64-encoded string of the image.
    """
    try:
        # Convert PDF bytes to an image
        images = pdf2image.convert_from_bytes(page_bytes)

        # Ensure we have an image (pdf2image returns a list)
        if not images:
            raise ValueError("No image could be extracted from the PDF.")

        # Convert the first image to bytes (assuming single-page PDF)
        image = images[0]

        # Save the image to a bytes buffer
        buffer = io.BytesIO()
        image.save(buffer, format="PNG")

        # Encode to base64
        base64_image = base64.b64encode(buffer.getvalue()).decode("utf-8")

        return base64_image

    except Exception as e:
        print(f"Error converting PDF page to base64: {e}")
        return None

def split_pdf_to_pages(pdf_path):
    """
    Splits the input PDF into a list of individual pages (as PDF data in bytes).

    :param pdf_path: The file path to the input PDF.
    :return: A list of byte strings, each representing a single-page PDF.
    """
    pages_data = []
    with open(pdf_path, 'rb') as pdf_file:
        reader = PyPDF2.PdfReader(pdf_file)
        for page_index in range(len(reader.pages)):
            writer = PyPDF2.PdfWriter()
            # Add the single page to a new PDF
            writer.add_page(reader.pages[page_index])
            
            # Write the PDF page to an in-memory buffer
            buffer = io.BytesIO()
            writer.write(buffer)
            
            # Append the resulting PDF bytes to the list
            pages_data.append(buffer.getvalue())

    return pages_data

async def process_pdf(pdf_path, process_page_func):
    """
    Processes the PDF by splitting it into pages and sending each page to the process_page function.

    :param pdf_path: The file path to the input PDF.
    :param process_page_func: A function that takes a single page's PDF bytes and returns a JSON object.
    :return: A list of JSON objects returned by process_page_func for each page.
    """
    pages = split_pdf_to_pages(pdf_path)
    json_results = []
    extract_tables_from_pdf_page(pages)

    #for page_number, page_bytes in enumerate(pages, start=1):
        # Send the single-page PDF bytes to the process_page function
        #page_json = await process_page_func(page_bytes, process_page_func)
        #json_results.append(page_json)
        #print(f"Processed page {page_number}")

    return json_results

def get_file_path():
    root = tk.Tk()
    root.withdraw()  # Hide the root window
    file_path = filedialog.askopenfilename(title="Select a file")
    return file_path

async def process_page(page_bytes, processing_func):
    """
    Processes a single-page PDF by writing it to a temporary file,
    invoking the provided processing function, and ensuring cleanup.

    :param page_bytes: Byte string of the single-page PDF.
    :param processing_func: Function that accepts a filepath and returns a JSON object.
    :return: JSON object returned by processing_func.
    """
    # Create a NamedTemporaryFile. delete=False allows the file to be closed before processing.
    with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_pdf:
        try:
            # Write the PDF bytes to the temporary file
            temp_pdf.write(page_bytes)
            temp_pdf.flush()  # Ensure all data is written to disk
            temp_pdf_path = temp_pdf.name
        except Exception as e:
            print(f"Error writing to temporary file: {e}")
            return None

    try:
        # ADD IN PAGE PROCESSING
        #result = processing_func(temp_pdf_path)
        result = '{}'
    except Exception as e:
        print(f"Error processing temporary file: {e}")
        result = None
    finally:
        # Ensure the temporary file is deleted
        try:
            os.remove(temp_pdf_path)
        except OSError as e:
            print(f"Error deleting temporary file: {e}")

    return result

# Example usage
async def main():
    pdf_path = get_file_path()  # Replace with your PDF file path

    # Process the PDF using the process_page function
    results = await process_pdf(pdf_path, process_page)

    # Optionally, save the results to a JSON file
    with open("output.json", "w") as json_file:
        json.dump(results, json_file, indent=4)

    print("Processing complete. Output saved to output.json.")

if __name__ == "__main__":
    asyncio.run(main())