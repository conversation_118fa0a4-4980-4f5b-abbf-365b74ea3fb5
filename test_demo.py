#!/usr/bin/env python3
"""
Simple test to verify the demo interface is working
"""

from flask import Flask, render_template

app = Flask(__name__)

@app.route('/')
def index():
    """Test route to check if templates are working"""
    return render_template('demo.html')

@app.route('/test')
def test():
    """Simple test route"""
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Test</title>
    </head>
    <body>
        <h1>Flask is working!</h1>
        <form>
            <label for="apiKey">API Key Test:</label>
            <input type="password" id="apiKey" name="api_key" placeholder="sk-test..." required>
            <button type="submit">Test</button>
        </form>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("Starting test demo...")
    print("Main demo: http://localhost:5001")
    print("Simple test: http://localhost:5001/test")
    app.run(debug=True, host='0.0.0.0', port=5001)
