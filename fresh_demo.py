#!/usr/bin/env python3
"""
Fresh Athena Demo Interface - Forces API key field to be visible
"""

import os
import time
from flask import Flask, render_template_string

app = Flask(__name__)

# Inline HTML template to avoid caching issues
DEMO_HTML = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Athena - Insurance Claim Document Processor</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; color: #333; }
        .container { max-width: 1200px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; color: white; margin-bottom: 30px; }
        .header h1 { font-size: 2.5rem; margin-bottom: 10px; text-shadow: 2px 2px 4px rgba(0,0,0,0.3); }
        .header p { font-size: 1.2rem; opacity: 0.9; }
        .upload-card { background: white; border-radius: 15px; padding: 30px; box-shadow: 0 10px 30px rgba(0,0,0,0.2); text-align: center; }
        .upload-card h2 { margin-bottom: 25px; color: #333; font-size: 1.8rem; }
        
        /* API Key Section - FORCED VISIBLE */
        .api-key-section { 
            margin-bottom: 25px; 
            padding: 20px; 
            background: #f8f9fa; 
            border-radius: 10px; 
            border: 3px solid #667eea !important; 
            display: block !important;
            visibility: visible !important;
        }
        .api-key-section .control-group { position: relative; display: flex; align-items: center; gap: 10px; width: 100%; }
        .api-key-section label { font-weight: 600; color: #333; font-size: 1.1rem; }
        .api-key-section input { 
            flex: 1; 
            padding: 12px 50px 12px 15px; 
            border: 2px solid #667eea; 
            border-radius: 8px; 
            font-size: 1rem; 
            font-family: 'Courier New', monospace; 
            background: white; 
        }
        .btn-toggle-key { 
            position: absolute; 
            right: 10px; 
            background: none; 
            border: none; 
            color: #667eea; 
            cursor: pointer; 
            padding: 8px; 
            border-radius: 4px; 
        }
        .api-key-help { margin-top: 8px; text-align: center; }
        .api-key-help small { color: #666; font-size: 0.85rem; }
        .api-key-help a { color: #667eea; text-decoration: none; }
        
        .file-upload input[type="file"] { display: none; }
        .file-label { 
            display: block; 
            padding: 40px 20px; 
            border: 3px dashed #ddd; 
            border-radius: 10px; 
            cursor: pointer; 
            background: #f8f9fa; 
            margin-bottom: 20px;
        }
        .file-label i { font-size: 3rem; color: #667eea; margin-bottom: 15px; display: block; }
        .file-label span { font-size: 1.1rem; color: #666; }
        
        .controls { display: flex; justify-content: space-between; align-items: center; margin-bottom: 25px; flex-wrap: wrap; gap: 20px; }
        .control-group { display: flex; align-items: center; gap: 10px; }
        .control-group label { font-weight: 600; color: #555; }
        .control-group select { padding: 8px 12px; border: 2px solid #ddd; border-radius: 5px; font-size: 1rem; }
        
        .btn-primary { 
            padding: 12px 25px; 
            border: none; 
            border-radius: 8px; 
            font-size: 1rem; 
            font-weight: 600; 
            cursor: pointer; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            color: white; 
            display: inline-flex; 
            align-items: center; 
            gap: 8px;
        }
        .btn-primary:hover { transform: translateY(-2px); }
        
        .checkbox-label { display: flex; align-items: center; cursor: pointer; font-weight: 600; color: #555; }
        .checkbox-label input[type="checkbox"] { margin-right: 8px; }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1><i class="fas fa-file-invoice"></i> Athena Demo</h1>
            <p>AI-Powered Insurance Claim Document Processing</p>
        </header>

        <div class="upload-section">
            <div class="upload-card">
                <h2>Upload PDF Document</h2>
                
                <!-- API KEY SECTION - FORCED VISIBLE -->
                <div class="api-key-section">
                    <div class="control-group">
                        <label for="apiKey">OpenAI API Key:</label>
                        <input type="password" id="apiKey" name="api_key" placeholder="sk-proj-..." required>
                        <button type="button" class="btn-toggle-key" onclick="toggleApiKeyVisibility()">
                            <i class="fas fa-eye" id="keyToggleIcon"></i>
                        </button>
                    </div>
                    <div class="api-key-help">
                        <small><i class="fas fa-info-circle"></i> Get your API key from <a href="https://platform.openai.com/api-keys" target="_blank">OpenAI Platform</a>. Your key is processed securely and not stored.</small>
                    </div>
                </div>
                
                <form id="uploadForm" enctype="multipart/form-data">
                    <div class="file-upload">
                        <input type="file" id="fileInput" name="file" accept=".pdf" required>
                        <label for="fileInput" class="file-label">
                            <i class="fas fa-cloud-upload-alt"></i>
                            <span>Choose PDF file or drag and drop</span>
                        </label>
                    </div>
                    
                    <div class="controls">
                        <div class="control-group">
                            <label for="maxPages">Pages to Process:</label>
                            <select id="maxPages" name="max_pages">
                                <option value="0">All Pages</option>
                                <option value="1">1 Page</option>
                                <option value="3">3 Pages</option>
                                <option value="5" selected>5 Pages</option>
                                <option value="10">10 Pages</option>
                            </select>
                        </div>
                        
                        <div class="control-group">
                            <label class="checkbox-label">
                                <input type="checkbox" id="useGpt41" name="use_gpt41" value="true">
                                Use GPT-4.1 (Enhanced Model)
                            </label>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-play"></i> Start Processing
                    </button>
                </form>
            </div>
        </div>
        
        <div id="status" style="color: white; text-align: center; margin-top: 20px; font-size: 1.2rem;"></div>
    </div>

    <script>
        function toggleApiKeyVisibility() {
            const apiKeyInput = document.getElementById('apiKey');
            const toggleIcon = document.getElementById('keyToggleIcon');
            
            if (apiKeyInput.type === 'password') {
                apiKeyInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                apiKeyInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }
        
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const apiKey = document.getElementById('apiKey').value.trim();
            const status = document.getElementById('status');
            
            if (!apiKey) {
                status.innerHTML = '❌ Please enter your OpenAI API key';
                document.getElementById('apiKey').focus();
                return;
            }
            
            if (!apiKey.startsWith('sk-')) {
                status.innerHTML = '❌ Please enter a valid OpenAI API key (should start with "sk-")';
                document.getElementById('apiKey').focus();
                return;
            }
            
            const fileInput = document.getElementById('fileInput');
            if (!fileInput.files[0]) {
                status.innerHTML = '❌ Please select a PDF file';
                return;
            }
            
            status.innerHTML = '✅ API Key and file validated! (Full processing would start here)';
        });
        
        // File input change handler
        document.getElementById('fileInput').addEventListener('change', function() {
            if (this.files[0]) {
                document.querySelector('.file-label span').textContent = this.files[0].name;
            }
        });
        
        // Show API key field status on load
        window.addEventListener('load', function() {
            console.log('API Key field loaded and visible!');
            const apiSection = document.querySelector('.api-key-section');
            if (apiSection) {
                console.log('API Key section found:', apiSection);
            } else {
                console.error('API Key section NOT found!');
            }
        });
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Fresh demo interface with inline template"""
    return render_template_string(DEMO_HTML)

@app.route('/test-api')
def test_api():
    """Test API key functionality"""
    return """
    <h1>API Key Test</h1>
    <p>If you can see this, the server is working.</p>
    <p><a href="/">Go to main demo</a></p>
    """

if __name__ == '__main__':
    print("🚀 Starting FRESH Athena Demo Interface...")
    print("📱 Open browser to: http://localhost:5002")
    print("🔑 The API key field is FORCED to be visible!")
    print("⏹️  Press Ctrl+C to stop")
    app.run(debug=True, host='0.0.0.0', port=5002)
